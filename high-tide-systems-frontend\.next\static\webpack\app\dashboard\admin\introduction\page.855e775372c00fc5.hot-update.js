"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/introduction/page",{

/***/ "(app-pages-browser)/./src/components/settings/BranchFormModal.js":
/*!****************************************************!*\
  !*** ./src/components/settings/BranchFormModal.js ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Check,Clock,Loader2,Mail,MapPin,Phone,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/modules/admin/services/branchService */ \"(app-pages-browser)/./src/app/modules/admin/services/branchService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_common_AddressForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/common/AddressForm */ \"(app-pages-browser)/./src/components/common/AddressForm.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_workingHours_BranchWorkingHoursForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/workingHours/BranchWorkingHoursForm */ \"(app-pages-browser)/./src/components/workingHours/BranchWorkingHoursForm.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_common_MaskedInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/common/MaskedInput */ \"(app-pages-browser)/./src/components/common/MaskedInput.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst BranchFormModal = (param)=>{\n    let { isOpen, onClose, branch, onSuccess } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const workingHoursFormRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        code: \"\",\n        description: \"\",\n        address: \"\",\n        neighborhood: \"\",\n        city: \"\",\n        state: \"\",\n        postalCode: \"\",\n        phone: \"\",\n        email: \"\",\n        isHeadquarters: false,\n        companyId: \"\",\n        defaultWorkingHours: null,\n        applyToUsers: false\n    });\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingCompanies, setLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"basic\"); // basic, workingHours\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BranchFormModal.useEffect\": ()=>{\n            // When editing an existing branch, fill the form\n            if (branch) {\n                setFormData({\n                    name: branch.name || \"\",\n                    code: branch.code || \"\",\n                    description: branch.description || \"\",\n                    address: branch.address || \"\",\n                    neighborhood: branch.neighborhood || \"\",\n                    city: branch.city || \"\",\n                    state: branch.state || \"\",\n                    postalCode: branch.postalCode || \"\",\n                    phone: branch.phone || \"\",\n                    email: branch.email || \"\",\n                    isHeadquarters: branch.isHeadquarters || false,\n                    companyId: branch.companyId || (user === null || user === void 0 ? void 0 : user.companyId) || \"\",\n                    defaultWorkingHours: branch.defaultWorkingHours || null,\n                    applyToUsers: false\n                });\n                // If branch exists but doesn't have default working hours, load them from API\n                if (branch.id && !branch.defaultWorkingHours) {\n                    loadDefaultWorkingHours(branch.id);\n                }\n            } else {\n                // New branch\n                resetForm();\n            }\n            // For system admins, load company options\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n        }\n    }[\"BranchFormModal.useEffect\"], [\n        branch,\n        isOpen,\n        user\n    ]);\n    const loadCompanies = async ()=>{\n        setLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_4__.companyService.getCompanies({\n                active: true,\n                limit: 100\n            });\n            setCompanies(response.companies || []);\n        } catch (error) {\n            console.error(\"Error loading companies:\", error);\n        } finally{\n            setLoadingCompanies(false);\n        }\n    };\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            code: \"\",\n            description: \"\",\n            address: \"\",\n            city: \"\",\n            state: \"\",\n            postalCode: \"\",\n            phone: \"\",\n            email: \"\",\n            isHeadquarters: false,\n            companyId: (user === null || user === void 0 ? void 0 : user.companyId) || \"\",\n            defaultWorkingHours: null,\n            applyToUsers: false\n        });\n        setErrors({});\n        setActiveTab(\"basic\");\n    };\n    const loadDefaultWorkingHours = async (branchId)=>{\n        try {\n            const data = await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_3__.branchService.getDefaultWorkingHours(branchId);\n            setFormData((prev)=>({\n                    ...prev,\n                    defaultWorkingHours: data.defaultWorkingHours\n                }));\n        } catch (error) {\n            console.error(\"Error loading default working hours:\", error);\n        // Don't set an error, just use the default working hours\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name) newErrors.name = \"Nome da unidade é obrigatório\";\n        if (!formData.address) newErrors.address = \"Endereço é obrigatório\";\n        if (!formData.companyId) newErrors.companyId = \"Empresa é obrigatória\";\n        if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Email inválido\";\n        }\n        if (formData.phone && !/^\\d{10,11}$/.test(formData.phone.replace(/\\D/g, ''))) {\n            newErrors.phone = \"Telefone inválido\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleChange = (e)=>{\n        const { name, value, checked, type } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === 'checkbox' ? checked : value\n            }));\n        // Clear errors when field is edited\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n    };\n    const handleWorkingHoursChange = (workingHours)=>{\n        console.log('BranchFormModal handleWorkingHoursChange - recebendo dados:', workingHours);\n        // Garante que não estamos sobrescrevendo o estado anterior com valores vazios\n        if (workingHours) {\n            setFormData((prev)=>{\n                const newFormData = {\n                    ...prev,\n                    defaultWorkingHours: workingHours\n                };\n                console.log('BranchFormModal handleWorkingHoursChange - novo estado:', newFormData.defaultWorkingHours);\n                return newFormData;\n            });\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validar o formulário básico\n        if (!validateForm()) return;\n        // Se estiver na aba de horários de trabalho, validar os horários\n        if (activeTab === \"workingHours\" && workingHoursFormRef.current) {\n            const isWorkingHoursValid = workingHoursFormRef.current.validateAllTimeSlots();\n            if (!isWorkingHoursValid) {\n                toast_error({\n                    title: \"Erro de validação\",\n                    message: \"Verifique os horários de trabalho e corrija os erros antes de salvar.\"\n                });\n                return;\n            }\n        }\n        setIsLoading(true);\n        try {\n            const payload = {\n                name: formData.name,\n                code: formData.code || undefined,\n                description: formData.description || undefined,\n                address: formData.address,\n                neighborhood: formData.neighborhood || undefined,\n                city: formData.city || undefined,\n                state: formData.state || undefined,\n                postalCode: formData.postalCode || undefined,\n                phone: formData.phone ? formData.phone.replace(/\\D/g, '') : undefined,\n                email: formData.email || undefined,\n                isHeadquarters: formData.isHeadquarters,\n                companyId: formData.companyId,\n                defaultWorkingHours: formData.defaultWorkingHours,\n                applyToUsers: formData.applyToUsers\n            };\n            let result;\n            if (branch) {\n                // Update existing branch\n                result = await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_3__.branchService.updateBranch(branch.id, payload);\n                // If applyToUsers is true, apply working hours to all users\n                if (formData.applyToUsers && formData.defaultWorkingHours) {\n                    try {\n                        await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_3__.branchService.applyWorkingHoursToUsers(branch.id);\n                        toast_success({\n                            title: \"Horários aplicados\",\n                            message: \"Horários de trabalho aplicados com sucesso aos usuários da unidade\"\n                        });\n                    } catch (error) {\n                        console.error(\"Error applying working hours to users:\", error);\n                        toast_error({\n                            title: \"Erro\",\n                            message: \"Erro ao aplicar horários de trabalho aos usuários\"\n                        });\n                    }\n                }\n            } else {\n                // Create new branch\n                result = await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_3__.branchService.createBranch(payload);\n            }\n            if (onSuccess) onSuccess();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error saving branch:\", error);\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) {\n                const apiErrors = {};\n                error.response.data.errors.forEach((err)=>{\n                    apiErrors[err.param] = err.msg;\n                });\n                setErrors(apiErrors);\n            } else {\n                var _error_response_data1, _error_response1;\n                setErrors({\n                    submit: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Erro ao salvar unidade\"\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // CSS classes with dark mode\n    const inputClasses = \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-neutral-900 dark:text-gray-100\";\n    const labelClasses = \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\";\n    const errorClasses = \"mt-1 text-xs text-red-600 dark:text-red-400\";\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[11050]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    branch ? \"Editar Unidade\" : \"Nova Unidade\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex border-b border-neutral-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"basic\"),\n                                className: \"px-6 py-3 font-medium text-sm transition-colors \".concat(activeTab === \"basic\" ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-500 dark:border-primary-400' : 'text-neutral-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveTab(\"workingHours\"),\n                                className: \"px-6 py-3 font-medium text-sm transition-colors \".concat(activeTab === \"workingHours\" ? 'text-primary-600 dark:text-primary-400 border-b-2 border-primary-500 dark:border-primary-400' : 'text-neutral-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Hor\\xe1rios de Trabalho\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            children: [\n                                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg\",\n                                    children: errors.submit\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, undefined),\n                                activeTab === \"basic\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary-500 dark:text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleFormGroup, {\n                                                moduleColor: \"admin\",\n                                                label: \"Empresa *\",\n                                                htmlFor: \"companyId\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                error: !!errors.companyId,\n                                                errorMessage: errors.companyId,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                                    moduleColor: \"admin\",\n                                                    id: \"companyId\",\n                                                    name: \"companyId\",\n                                                    value: formData.companyId,\n                                                    onChange: handleChange,\n                                                    disabled: isLoading || loadingCompanies,\n                                                    required: true,\n                                                    placeholder: \"Selecione uma empresa\",\n                                                    error: !!errors.companyId,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Selecione uma empresa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        loadingCompanies ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            disabled: true,\n                                                            children: \"Carregando empresas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, undefined) : companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: company.id,\n                                                                children: company.name\n                                                            }, company.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleFormGroup, {\n                                                moduleColor: \"admin\",\n                                                label: \"Nome da Unidade *\",\n                                                htmlFor: \"name\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                error: !!errors.name,\n                                                errorMessage: errors.name,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleInput, {\n                                                    moduleColor: \"admin\",\n                                                    id: \"name\",\n                                                    name: \"name\",\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: handleChange,\n                                                    placeholder: \"Nome da unidade\",\n                                                    disabled: isLoading,\n                                                    required: true,\n                                                    error: !!errors.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleFormGroup, {\n                                                moduleColor: \"admin\",\n                                                label: \"C\\xf3digo\",\n                                                htmlFor: \"code\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleInput, {\n                                                        moduleColor: \"admin\",\n                                                        id: \"code\",\n                                                        name: \"code\",\n                                                        type: \"text\",\n                                                        value: formData.code,\n                                                        onChange: handleChange,\n                                                        placeholder: \"C\\xf3digo \\xfanico (opcional)\",\n                                                        disabled: isLoading\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                        children: \"C\\xf3digo \\xfanico para identifica\\xe7\\xe3o da unidade\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleFormGroup, {\n                                                moduleColor: \"admin\",\n                                                label: \"Descri\\xe7\\xe3o\",\n                                                htmlFor: \"description\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleInput, {\n                                                    moduleColor: \"admin\",\n                                                    id: \"description\",\n                                                    name: \"description\",\n                                                    type: \"textarea\",\n                                                    value: formData.description,\n                                                    onChange: handleChange,\n                                                    rows: \"3\",\n                                                    placeholder: \"Breve descri\\xe7\\xe3o da unidade\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary-500 dark:text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Endere\\xe7o\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_AddressForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                formData: formData,\n                                                setFormData: setFormData,\n                                                errors: errors,\n                                                isLoading: isLoading,\n                                                fieldMapping: {\n                                                    // Mapeamento personalizado para os campos da API ViaCEP\n                                                    logradouro: \"address\",\n                                                    bairro: \"neighborhood\",\n                                                    localidade: \"city\",\n                                                    uf: \"state\",\n                                                    cep: \"postalCode\"\n                                                },\n                                                classes: {\n                                                    label: labelClasses,\n                                                    input: inputClasses,\n                                                    error: errorClasses\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary-500 dark:text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Contato\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 474,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"phone\",\n                                                    children: \"Telefone\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_MaskedInput__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    type: \"phone\",\n                                                    value: formData.phone,\n                                                    onChange: (e)=>handleChange({\n                                                            target: {\n                                                                name: \"phone\",\n                                                                value: e.target.value\n                                                            }\n                                                        }),\n                                                    placeholder: \"(00) 00000-0000\",\n                                                    className: \"\".concat(inputClasses, \" \").concat(errors.phone ? \"border-red-500 dark:border-red-700\" : \"\"),\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: errorClasses,\n                                                    children: errors.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 34\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"email\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    value: formData.email,\n                                                    onChange: handleChange,\n                                                    className: \"\".concat(inputClasses, \" \").concat(errors.email ? \"border-red-500 dark:border-red-700\" : \"\"),\n                                                    placeholder: \"<EMAIL>\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: errorClasses,\n                                                    children: errors.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 34\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"isHeadquarters\",\n                                                            name: \"isHeadquarters\",\n                                                            type: \"checkbox\",\n                                                            checked: formData.isHeadquarters,\n                                                            onChange: handleChange,\n                                                            className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded\",\n                                                            disabled: isLoading || branch && branch.isHeadquarters\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"isHeadquarters\",\n                                                            className: \"flex items-center gap-1 text-sm text-neutral-800 dark:text-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-amber-500 dark:text-amber-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"Definir como matriz/sede principal\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"ml-6 mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                    children: \"Apenas uma unidade pode ser definida como matriz por empresa. Isso alterar\\xe1 automaticamente o status de outras unidades.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, undefined),\n                                activeTab === \"workingHours\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workingHours_BranchWorkingHoursForm__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            ref: workingHoursFormRef,\n                                            defaultWorkingHours: formData.defaultWorkingHours,\n                                            onChange: handleWorkingHoursChange,\n                                            isLoading: isLoading,\n                                            labelClasses: labelClasses,\n                                            inputClasses: inputClasses,\n                                            errorClasses: errorClasses,\n                                            onValidationChange: (isValid)=>{\n                                            // Opcional: Podemos usar isso para atualizar o estado do botão de salvar\n                                            // ou mostrar um indicador visual de validação\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        branch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"applyToUsers\",\n                                                            name: \"applyToUsers\",\n                                                            type: \"checkbox\",\n                                                            checked: formData.applyToUsers,\n                                                            onChange: handleChange,\n                                                            className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"applyToUsers\",\n                                                            className: \"text-sm text-neutral-800 dark:text-gray-200\",\n                                                            children: \"Aplicar hor\\xe1rios a todos os usu\\xe1rios desta unidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"ml-6 mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                    children: \"Marque esta op\\xe7\\xe3o para aplicar os hor\\xe1rios de trabalho a todos os usu\\xe1rios j\\xe1 associados a esta unidade\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 557,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onClose,\n                                className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                disabled: isLoading,\n                                children: \"Cancelar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 584,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\",\n                                disabled: isLoading,\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: 16,\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 600,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Salvando...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Check_Clock_Loader2_Mail_MapPin_Phone_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Salvar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 606,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                        lineNumber: 583,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BranchFormModal, \"7/5COt4384kZ5MHjGmr8gKFZCz8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = BranchFormModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BranchFormModal);\nvar _c;\n$RefreshReg$(_c, \"BranchFormModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/BranchFormModal.js\n"));

/***/ })

});