/* Padrão de fundo para o banner de perfil */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Garantir que os selects na página de perfil tenham um z-index adequado */
.profile-select-container {
  position: relative !important;
  z-index: 1 !important; /* Reduzido para que o dropdown fique acima */
  overflow: visible !important;
}

/* Garantir que os dropdowns dos selects fiquem visíveis */
.profile-select-dropdown {
  position: absolute !important;
  z-index: 1000 !important; /* Usar valor do design system para dropdowns */
  width: 100% !important;
  top: 100% !important;
  left: 0 !important;
  margin-top: 4px !important;
  max-height: 200px !important;
  overflow-y: auto !important;
  background-color: white !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border: 1px solid #e5e7eb !important;
  transform: translateZ(0) !important; /* Força o uso de um novo contexto de empilhamento */
}

/* Estilo para o modo escuro */
@media (prefers-color-scheme: dark) {
  .profile-select-dropdown {
    background-color: #1f2937 !important;
    border-color: #374151 !important;
  }
}

/* Garantir que o formulário de perfil tenha um z-index adequado */
.profile-form {
  position: relative !important;
  z-index: 1 !important;
}

/* Garantir que os selects não sejam cortados por outros elementos */
.profile-select-wrapper {
  position: relative !important;
  overflow: visible !important;
}

/* Quando o dropdown está aberto, aumentar o z-index do container */
.dropdown-open {
  z-index: 999 !important; /* Menor que o dropdown (1000) */
  position: relative !important;
}
