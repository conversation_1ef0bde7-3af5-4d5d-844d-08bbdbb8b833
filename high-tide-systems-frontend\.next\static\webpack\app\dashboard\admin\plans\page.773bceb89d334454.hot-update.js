"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/components/permissions/PermissionsModal.js":
/*!********************************************************!*\
  !*** ./src/components/permissions/PermissionsModal.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* harmony import */ var _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/permissionConfig */ \"(app-pages-browser)/./src/utils/permissionConfig.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PermissionsModal = (param)=>{\n    let { isOpen, onClose, user, onSuccess } = param;\n    var _currentUser_modules, _user_modules, _user_modules1;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [selectedPermissions, setSelectedPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [expandedModules, setExpandedModules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredPermissions, setFilteredPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Carregar permissões do usuário ao abrir o modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PermissionsModal.useEffect\": ()=>{\n            if (user && isOpen) {\n                var _user_modules;\n                setIsLoading(true);\n                // Se usuário já tem módulos/permissões, configuramos o estado inicial\n                if (user.permissions) {\n                    setSelectedPermissions(user.permissions);\n                } else {\n                    // Se não tiver permissões, inicializamos com array vazio\n                    setSelectedPermissions([]);\n                }\n                // Inicialmente, expandimos apenas os módulos que o usuário tem acesso\n                const initialExpandedState = {};\n                (_user_modules = user.modules) === null || _user_modules === void 0 ? void 0 : _user_modules.forEach({\n                    \"PermissionsModal.useEffect\": (moduleId)=>{\n                        initialExpandedState[moduleId] = true;\n                    }\n                }[\"PermissionsModal.useEffect\"]);\n                setExpandedModules(initialExpandedState);\n                setIsLoading(false);\n            }\n        }\n    }[\"PermissionsModal.useEffect\"], [\n        user,\n        isOpen\n    ]);\n    // Filtragem de permissões baseada na busca\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PermissionsModal.useEffect\": ()=>{\n            if (!searchTerm.trim()) {\n                setFilteredPermissions((0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getAllPermissions)());\n                return;\n            }\n            const lowerSearch = searchTerm.toLowerCase();\n            const filtered = (0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getAllPermissions)().filter({\n                \"PermissionsModal.useEffect.filtered\": (permission)=>permission.name.toLowerCase().includes(lowerSearch) || permission.description.toLowerCase().includes(lowerSearch) || permission.id.toLowerCase().includes(lowerSearch) || _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[permission.moduleId].name.toLowerCase().includes(lowerSearch)\n            }[\"PermissionsModal.useEffect.filtered\"]);\n            setFilteredPermissions(filtered);\n            // Expande automaticamente os módulos que têm permissões que correspondem à busca\n            const modulesToExpand = {};\n            filtered.forEach({\n                \"PermissionsModal.useEffect\": (permission)=>{\n                    modulesToExpand[permission.moduleId] = true;\n                }\n            }[\"PermissionsModal.useEffect\"]);\n            setExpandedModules({\n                \"PermissionsModal.useEffect\": (prev)=>({\n                        ...prev,\n                        ...modulesToExpand\n                    })\n            }[\"PermissionsModal.useEffect\"]);\n        }\n    }[\"PermissionsModal.useEffect\"], [\n        searchTerm\n    ]);\n    const isAdmin = currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_modules = currentUser.modules) === null || _currentUser_modules === void 0 ? void 0 : _currentUser_modules.includes(\"ADMIN\");\n    // Verificar se o usuário tem uma permissão específica\n    const hasPermission = (permissionId)=>{\n        return selectedPermissions.includes(permissionId);\n    };\n    // Alternar uma permissão específica\n    const togglePermission = (permissionId)=>{\n        setSelectedPermissions((prev)=>{\n            if (prev.includes(permissionId)) {\n                return prev.filter((id)=>id !== permissionId);\n            } else {\n                return [\n                    ...prev,\n                    permissionId\n                ];\n            }\n        });\n    };\n    // Alternar todas as permissões de um módulo\n    const toggleModulePermissions = (moduleId)=>{\n        const modulePermissions = _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId].permissions.map((p)=>p.id);\n        // Verificar se todas as permissões do módulo já estão selecionadas\n        const allSelected = modulePermissions.every((p)=>selectedPermissions.includes(p));\n        if (allSelected) {\n            // Remover todas as permissões do módulo\n            setSelectedPermissions((prev)=>prev.filter((p)=>!modulePermissions.includes(p)));\n        } else {\n            // Adicionar todas as permissões do módulo\n            setSelectedPermissions((prev)=>{\n                const newPermissions = [\n                    ...prev\n                ];\n                modulePermissions.forEach((p)=>{\n                    if (!newPermissions.includes(p)) {\n                        newPermissions.push(p);\n                    }\n                });\n                return newPermissions;\n            });\n        }\n    };\n    // Alternar a expansão de um módulo\n    const toggleModuleExpansion = (moduleId)=>{\n        setExpandedModules((prev)=>({\n                ...prev,\n                [moduleId]: !prev[moduleId]\n            }));\n    };\n    // Salvar permissões\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        setError(\"\");\n        try {\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__.userService.updatePermissions(user.id, selectedPermissions);\n            onSuccess();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao atualizar permissões:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao atualizar permissões\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    // Obter o ícone do módulo\n    const getModuleIcon = (moduleId)=>{\n        const icons = {\n            ADMIN: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 169,\n                columnNumber: 14\n            }, undefined),\n            RH: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 170,\n                columnNumber: 11\n            }, undefined),\n            FINANCIAL: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 171,\n                columnNumber: 18\n            }, undefined),\n            SCHEDULING: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 172,\n                columnNumber: 19\n            }, undefined),\n            BASIC: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 173,\n                columnNumber: 14\n            }, undefined)\n        };\n        return icons[moduleId] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n            lineNumber: 176,\n            columnNumber: 31\n        }, undefined);\n    };\n    // Renderizar as permissões de um módulo\n    const renderModulePermissions = (moduleId)=>{\n        const module = _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId];\n        if (!module) return null;\n        // Se estiver filtrando, mostrar apenas as permissões que correspondem à busca\n        const permissions = searchTerm ? module.permissions.filter((p)=>filteredPermissions.some((fp)=>fp.id === p.id)) : module.permissions;\n        if (permissions.length === 0) return null;\n        const allPermissionsSelected = permissions.every((p)=>selectedPermissions.includes(p.id));\n        const somePermissionsSelected = permissions.some((p)=>selectedPermissions.includes(p.id));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 border rounded-lg overflow-hidden dark:border-gray-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between cursor-pointer border-b dark:border-gray-700\",\n                    onClick: ()=>toggleModuleExpansion(moduleId),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 rounded-full \".concat(somePermissionsSelected ? allPermissionsSelected ? \"bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\" : \"bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400\" : \"bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400\"),\n                                    children: getModuleIcon(moduleId)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-neutral-800 dark:text-gray-200\",\n                                            children: module.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                            children: somePermissionsSelected ? \"\".concat(selectedPermissions.filter((p)=>module.permissions.some((mp)=>mp.id === p)).length, \" de \").concat(permissions.length, \" permiss\\xf5es selecionadas\") : \"Nenhuma permissão selecionada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        toggleModulePermissions(moduleId);\n                                    },\n                                    className: \"px-3 py-1 rounded text-sm font-medium \".concat(allPermissionsSelected ? \"bg-neutral-200 text-neutral-700 hover:bg-neutral-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600\" : \"bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700\"),\n                                    children: allPermissionsSelected ? \"Desmarcar todas\" : \"Selecionar todas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined),\n                                expandedModules[moduleId] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"text-neutral-600 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"text-neutral-600 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, undefined),\n                expandedModules[moduleId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 divide-y dark:divide-gray-700 dark:bg-gray-850\",\n                    children: permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-3 first:pt-0 last:pb-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mt-0.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: permission.id,\n                                            checked: hasPermission(permission.id),\n                                            onChange: ()=>togglePermission(permission.id),\n                                            className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 265,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 264,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: permission.id,\n                                                className: \"block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer\",\n                                                children: permission.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-neutral-600 dark:text-gray-400\",\n                                                children: permission.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 281,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1 text-xs text-neutral-500 dark:text-gray-500\",\n                                                children: [\n                                                    \"ID: \",\n                                                    permission.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 263,\n                                columnNumber: 17\n                            }, undefined)\n                        }, permission.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                            lineNumber: 262,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                    lineNumber: 260,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, moduleId, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, undefined);\n    };\n    if (false) {}\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 dark:bg-black/70\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-4xl w-full max-h-[90vh] flex flex-col z-[11050]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-neutral-800 dark:text-gray-100\",\n                                        children: \"Gerenciar Permiss\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-neutral-500 hover:text-neutral-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto p-6 dark:bg-gray-800\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg flex items-center gap-2 dark:bg-red-900/20 dark:border-red-800/50 dark:text-red-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\",\n                                        children: user === null || user === void 0 ? void 0 : user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-600 dark:text-gray-400 mb-4\",\n                                        children: \"Configure as permiss\\xf5es espec\\xedficas que este usu\\xe1rio ter\\xe1 acesso dentro de cada m\\xf3dulo:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-amber-50 border border-amber-200 p-4 rounded-lg flex items-start gap-3 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 mt-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-5 w-5 text-amber-500 dark:text-amber-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-medium text-amber-800 dark:text-amber-300\",\n                                                        children: \"Importante\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-amber-700 dark:text-amber-400\",\n                                                        children: \"As permiss\\xf5es s\\xf3 ser\\xe3o aplicadas se o usu\\xe1rio tamb\\xe9m tiver acesso ao m\\xf3dulo correspondente. Certifique-se de que o usu\\xe1rio tenha os m\\xf3dulos necess\\xe1rios atribu\\xeddos.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-neutral-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Buscar permiss\\xf5es...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 dark:placeholder-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 359,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                onClick: ()=>setSearchTerm(\"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-neutral-400 hover:text-neutral-600 dark:text-gray-500 dark:hover:text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin text-primary-500 dark:text-primary-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg mb-6 dark:border dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                                children: \"M\\xf3dulos Atribu\\xeddos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    user === null || user === void 0 ? void 0 : (_user_modules = user.modules) === null || _user_modules === void 0 ? void 0 : _user_modules.map((moduleId)=>{\n                                                        var _PERMISSIONS_CONFIG_moduleId;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-1.5 bg-white dark:bg-gray-700 border dark:border-gray-600 rounded-full flex items-center gap-2\",\n                                                            children: [\n                                                                getModuleIcon(moduleId),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm dark:text-gray-300\",\n                                                                    children: ((_PERMISSIONS_CONFIG_moduleId = _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId]) === null || _PERMISSIONS_CONFIG_moduleId === void 0 ? void 0 : _PERMISSIONS_CONFIG_moduleId.name) || moduleId\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, moduleId, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, undefined);\n                                                    }),\n                                                    (!(user === null || user === void 0 ? void 0 : user.modules) || user.modules.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                                        children: \"Nenhum m\\xf3dulo atribu\\xeddo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    searchTerm ? // Se estiver pesquisando, mostra os módulos que têm permissões correspondentes\n                                    Object.keys(_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG).filter((moduleId)=>_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId].permissions.some((p)=>filteredPermissions.some((fp)=>fp.id === p.id))).map((moduleId)=>renderModulePermissions(moduleId)) : // Caso contrário, mostra primeiro os módulos que o usuário tem acesso\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            user === null || user === void 0 ? void 0 : (_user_modules1 = user.modules) === null || _user_modules1 === void 0 ? void 0 : _user_modules1.map((moduleId)=>renderModulePermissions(moduleId)),\n                                            Object.keys(_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG).filter((moduleId)=>{\n                                                var _user_modules;\n                                                return !(user === null || user === void 0 ? void 0 : (_user_modules = user.modules) === null || _user_modules === void 0 ? void 0 : _user_modules.includes(moduleId));\n                                            }).map((moduleId)=>renderModulePermissions(moduleId))\n                                        ]\n                                    }, void 0, true),\n                                    searchTerm && filteredPermissions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-neutral-500 dark:text-gray-400\",\n                                            children: [\n                                                'Nenhuma permiss\\xe3o encontrada para \"',\n                                                searchTerm,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 433,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 432,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-neutral-600 dark:text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: selectedPermissions.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"permiss\\xf5es selecionadas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                        disabled: isSaving,\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleSave,\n                                        className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\",\n                                        disabled: isSaving,\n                                        children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvar Permiss\\xf5es\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PermissionsModal, \"bAeQaxmiSAKYMfKURw7MVm+i1Ck=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = PermissionsModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PermissionsModal);\nvar _c;\n$RefreshReg$(_c, \"PermissionsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/permissions/PermissionsModal.js\n"));

/***/ })

});