"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/components.js":
/*!*****************************************!*\
  !*** ./src/app/dashboard/components.js ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header),\n/* harmony export */   NavLink: () => (/* binding */ NavLink),\n/* harmony export */   moduleSubmenus: () => (/* binding */ moduleSubmenus),\n/* harmony export */   modules: () => (/* binding */ modules)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-list.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart,Bell,BookOpen,Box,Brain,Briefcase,Building,Calculator,Calendar,ChevronDown,ChevronRight,ClipboardList,Clock,Construction,CreditCard,Database,DollarSign,FileText,Gift,GraduationCap,HardHat,Home,Info,LayoutDashboard,Lock,LogOut,MapPin,Menu,MessageCircle,Search,Settings,Shield,ShieldCheck,ShieldIcon,Tag,TrendingUp,UserCheck,UserCog,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _components_chat__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat */ \"(app-pages-browser)/./src/components/chat/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.js\");\n/* harmony import */ var _config_appConfig__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/config/appConfig */ \"(app-pages-browser)/./src/config/appConfig.js\");\n/* __next_internal_client_entry_do_not_use__ modules,moduleSubmenus,Header,NavLink auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Header Component\nconst Header = (param)=>{\n    let { toggleSidebar, isSidebarOpen } = param;\n    var _user_company;\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const { openQuickNav } = (0,_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav)();\n    // Função para determinar o ícone e as cores do papel do usuário\n    const getRoleInfo = ()=>{\n        switch(user === null || user === void 0 ? void 0 : user.role){\n            case 'SYSTEM_ADMIN':\n                return {\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                    bgColor: 'bg-red-50 dark:bg-red-900',\n                    textColor: 'text-red-700 dark:text-red-300',\n                    name: 'Admin do Sistema'\n                };\n            case 'COMPANY_ADMIN':\n                return {\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                    bgColor: 'bg-blue-50 dark:bg-blue-900',\n                    textColor: 'text-blue-700 dark:text-blue-300',\n                    name: 'Admin da Empresa'\n                };\n            default:\n                return {\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                    bgColor: 'bg-green-50 dark:bg-green-900',\n                    textColor: 'text-green-700 dark:text-green-300',\n                    name: 'Funcionário'\n                };\n        }\n    };\n    const roleInfo = getRoleInfo();\n    const RoleIcon = roleInfo.icon;\n    // Pegar primeira letra de cada nome para o avatar\n    const getInitials = ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.fullName)) return 'U';\n        const names = user.fullName.split(' ');\n        if (names.length === 1) return names[0].charAt(0);\n        return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9000]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors\",\n                        \"aria-label\": isSidebarOpen ? \"Fechar menu lateral\" : \"Abrir menu lateral\",\n                        children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                            lineNumber: 76,\n                            columnNumber: 28\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                            lineNumber: 76,\n                            columnNumber: 65\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo_horizontal_sem_fundo.png\",\n                                    alt: \"High Tide Logo\",\n                                    className: \"h-10 mr-2.5 dark:invert dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\",\n                                    children: _config_appConfig__WEBPACK_IMPORTED_MODULE_9__.APP_VERSION\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openQuickNav,\n                        className: \"flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors\",\n                        \"aria-label\": \"Abrir pesquisa r\\xe1pida\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 18,\n                                className: \"text-gray-400 dark:text-gray-500\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Pesquisar...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ctrl + K\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat__WEBPACK_IMPORTED_MODULE_6__.ChatButton, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_5__.ConstructionButton, {\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full relative transition-colors\",\n                        \"aria-label\": \"Notifica\\xe7\\xf5es\",\n                        title: \"Sistema de Notifica\\xe7\\xf5es\",\n                        content: \"O sistema de notifica\\xe7\\xf5es est\\xe1 em desenvolvimento e estar\\xe1 dispon\\xedvel em breve. Voc\\xea receber\\xe1 alertas sobre eventos importantes no sistema.\",\n                        icon: \"Bell\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute top-1 right-1 h-2 w-2 rounded-full bg-primary-500\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/admin/settings'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\",\n                        \"aria-label\": \"Configura\\xe7\\xf5es\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_8__.ThemeToggle, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 border-l border-gray-200 dark:border-gray-700 mx-1\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                \"aria-expanded\": \"false\",\n                                \"aria-haspopup\": \"true\",\n                                \"aria-label\": \"Menu do usu\\xe1rio\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-9 w-9 rounded-full flex items-center justify-center font-medium overflow-hidden\",\n                                        children: (user === null || user === void 0 ? void 0 : user.profileImageFullUrl) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: user.profileImageFullUrl,\n                                            alt: \"Foto de perfil de \".concat((user === null || user === void 0 ? void 0 : user.fullName) || 'Usuário'),\n                                            className: \"h-10 w-10 rounded-full object-cover\",\n                                            onError: (e)=>{\n                                                e.target.onerror = null;\n                                                e.target.style.display = 'none';\n                                                e.target.parentNode.innerHTML = getInitials();\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, undefined) : getInitials()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1\",\n                                                children: (user === null || user === void 0 ? void 0 : user.fullName) || 'Usuário'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs \".concat(roleInfo.textColor, \" px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 \").concat(roleInfo.bgColor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleIcon, {\n                                                        size: 10,\n                                                        className: \"mr-1\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: roleInfo.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-gray-400 dark:text-gray-500 hidden md:block\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right\",\n                                role: \"menu\",\n                                \"aria-orientation\": \"vertical\",\n                                \"aria-labelledby\": \"user-menu-button\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200\",\n                                                children: (user === null || user === void 0 ? void 0 : user.fullName) || 'Usuário'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                children: \"Empresa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 182,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200\",\n                                                children: (user === null || user === void 0 ? void 0 : (_user_company = user.company) === null || _user_company === void 0 ? void 0 : _user_company.name) || 'Minha Empresa'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-100 dark:border-gray-700 pt-1 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/profile'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meu Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 187,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: logout,\n                                                className: \"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors\",\n                                                role: \"menuitem\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"mr-2\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Sair do Sistema\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                        lineNumber: 186,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"lxBQFzzV6dgk+z4JHIb9l7reeX8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav\n    ];\n});\n_c = Header;\n// Navigation Link Component - Atualizado para usar o sistema de temas por módulo\nconst NavLink = (param)=>{\n    let { icon: Icon, title, href, isAccessible, moduleId = 'scheduler' } = param;\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname)();\n    // Verifica se o link está ativo comparando com o pathname atual\n    const isActive = pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>router.push(href),\n        disabled: !isAccessible,\n        className: \"\\n        w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-colors\\n        \".concat(isActive ? \"bg-module-\".concat(moduleId, \"-bg text-module-\").concat(moduleId, \"-icon\") : isAccessible ? 'text-gray-600 hover:bg-gray-100' : 'opacity-50 cursor-not-allowed', \"\\n      \"),\n        \"aria-current\": isActive ? 'page' : undefined,\n        role: \"link\",\n        \"aria-disabled\": !isAccessible,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                size: 20,\n                \"aria-hidden\": \"true\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-medium\",\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\components.js\",\n        lineNumber: 219,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(NavLink, \"gA9e4WsoP6a20xDgQgrFkfMP8lc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.usePathname\n    ];\n});\n_c1 = NavLink;\n// Configuração dos módulos principais - mantida igual\nconst modules = [\n    {\n        id: 'admin',\n        title: 'Administração',\n        icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        description: 'Gerencie todo o sistema, incluindo usuários e configurações.',\n        role: 'ADMIN'\n    },\n    // {\n    //   id: 'financial',\n    //   title: 'Financeiro',\n    //   icon: DollarSign,\n    //   description: 'Controle receitas, despesas e gere relatórios financeiros detalhados.',\n    //   role: 'FINANCIAL'\n    // },\n    // {\n    //   id: 'hr',\n    //   title: 'RH',\n    //   icon: Users,\n    //   description: 'Gerencie informações de funcionários, admissões e folha de pagamento.',\n    //   role: 'RH'\n    // },\n    {\n        id: 'people',\n        title: 'Pessoas',\n        icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        description: 'Cadastre e gerencie informações de pacientes e clientes.',\n        role: 'BASIC'\n    },\n    {\n        id: 'scheduler',\n        title: 'Agendamento',\n        icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n        description: 'Agende e gerencie compromissos, consultas e eventos.',\n        role: 'BASIC'\n    },\n    {\n        id: 'abaplus',\n        title: 'ABA+',\n        icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        description: 'Gerencie programas ABA, acompanhamento terapêutico.',\n        role: 'BASIC'\n    }\n];\n// Submódulos para cada módulo principal - apenas ABA+ com grupos\nconst moduleSubmenus = {\n    admin: [\n        {\n            id: 'introduction',\n            title: 'Introdução',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: 'Visão geral do módulo de administração'\n        },\n        {\n            id: 'users',\n            title: 'Usuários',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: 'Gerenciar usuários do sistema'\n        },\n        {\n            id: 'professions',\n            title: 'Profissões',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            description: 'Gerenciar profissões e grupos'\n        },\n        {\n            id: 'plans',\n            title: 'Planos',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            description: 'Gerenciar plano e assinatura'\n        },\n        {\n            id: 'settings',\n            title: 'Configurações',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: 'Configurações gerais do sistema'\n        },\n        {\n            id: 'logs',\n            title: 'Logs',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: 'Histórico de atividades do sistema'\n        },\n        {\n            id: 'backup',\n            title: 'Backup',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n            description: 'Gerenciamento de backup dos dados'\n        },\n        {\n            id: 'dashboard',\n            title: 'Dashboard',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n            description: 'Visão geral do sistema'\n        }\n    ],\n    financial: [\n        {\n            id: 'invoices',\n            title: 'Faturas',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: 'Gerenciar faturas e cobranças'\n        },\n        {\n            id: 'payments',\n            title: 'Pagamentos',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            description: 'Controle de pagamentos'\n        },\n        {\n            id: 'expenses',\n            title: 'Despesas',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"],\n            description: 'Gestão de despesas'\n        },\n        {\n            id: 'reports',\n            title: 'Relatórios',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n            description: 'Relatórios financeiros'\n        },\n        {\n            id: 'cashflow',\n            title: 'Fluxo de Caixa',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"],\n            description: 'Análise de fluxo de caixa'\n        }\n    ],\n    hr: [\n        {\n            id: 'employees',\n            title: 'Funcionários',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: 'Gerenciar funcionários'\n        },\n        {\n            id: 'payroll',\n            title: 'Folha de Pagamento',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"],\n            description: 'Processamento de salários'\n        },\n        {\n            id: 'documents',\n            title: 'Documentos',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: 'Documentos e formulários de RH'\n        },\n        {\n            id: 'departments',\n            title: 'Departamentos',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"],\n            description: 'Gestão de departamentos'\n        },\n        {\n            id: 'attendance',\n            title: 'Ponto',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n            description: 'Controle de ponto e ausências'\n        },\n        {\n            id: 'benefits',\n            title: 'Benefícios',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n            description: 'Gestão de benefícios'\n        },\n        {\n            id: 'training',\n            title: 'Treinamentos',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"],\n            description: 'Gestão de treinamentos'\n        }\n    ],\n    people: [\n        {\n            id: 'introduction',\n            title: 'Introdução',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: 'Visão geral do módulo de pessoas'\n        },\n        {\n            id: 'clients',\n            title: 'Clientes',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"],\n            description: 'Gerenciar clientes e contas'\n        },\n        {\n            id: 'persons',\n            title: 'Pacientes',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: 'Gerenciar cadastro de pacientes'\n        },\n        {\n            id: 'insurances',\n            title: 'Convênios',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            description: 'Gerenciar convênios associados'\n        },\n        {\n            id: 'insurance-limits',\n            title: 'Limites de Convênio',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: 'Gerenciar limites de serviço por convênio'\n        },\n        {\n            id: 'dashboard',\n            title: 'Dashboard',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n            description: 'Análise e estatísticas de pessoas'\n        }\n    ],\n    scheduler: [\n        {\n            id: 'introduction',\n            title: 'Introdução',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: 'Visão geral do módulo de agendamento'\n        },\n        {\n            id: 'calendar',\n            title: 'Agendar Consulta',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: 'Visualizar agenda completa'\n        },\n        {\n            id: 'working-hours',\n            title: 'Horários de Trabalho',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"],\n            description: 'Configurar horários de trabalho'\n        },\n        {\n            id: 'service-types',\n            title: 'Tipos de Serviço',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"],\n            description: 'Gerenciar tipos de serviço'\n        },\n        {\n            id: 'locations',\n            title: 'Localizações',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"],\n            description: 'Gerenciar localizações e endereços'\n        },\n        {\n            id: 'occupancy',\n            title: 'Ocupação',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            description: 'Análise detalhada da ocupação dos profissionais'\n        },\n        {\n            id: 'appointments-report',\n            title: 'Relatório',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n            description: 'Gerenciar agendamentos em formato de lista'\n        },\n        {\n            id: 'appointments-dashboard',\n            title: 'Dashboard',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n            description: 'Análise de agendamentos e estatísticas'\n        }\n    ],\n    abaplus: [\n        {\n            id: 'introduction',\n            title: 'Introdução',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: 'Visão geral do módulo ABA+'\n        },\n        {\n            id: 'dashboard',\n            title: 'Dashboard',\n            icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n            description: 'Análise e estatísticas ABA+'\n        },\n        {\n            id: 'cadastro',\n            title: 'Cadastro',\n            type: 'group',\n            items: [\n                {\n                    id: 'skills',\n                    title: 'Habilidades',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"],\n                    description: 'Gerenciar habilidades e competências'\n                },\n                {\n                    id: 'programs',\n                    title: 'Programas',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"],\n                    description: 'Gerenciar programas terapêuticos'\n                },\n                {\n                    id: 'evaluations',\n                    title: 'Avaliações',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n                    description: 'Gerenciar avaliações e protocolos'\n                },\n                {\n                    id: 'standard-criteria',\n                    title: 'Critérios Padrão',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_44__[\"default\"],\n                    description: 'Gerenciar critérios padrão'\n                },\n                {\n                    id: 'curriculum-folders',\n                    title: 'Pastas Curriculares',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                    description: 'Gerenciar pastas curriculares dos aprendizes'\n                }\n            ]\n        },\n        {\n            id: 'atendimento',\n            title: 'Atendimento',\n            type: 'group',\n            items: [\n                {\n                    id: 'anamnese',\n                    title: 'Anamnese',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_43__[\"default\"],\n                    description: 'Gerenciar anamneses dos pacientes'\n                },\n                {\n                    id: 'evolucoes-diarias',\n                    title: 'Evoluções Diárias',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                    description: 'Gerenciar evoluções diárias dos atendimentos'\n                },\n                {\n                    id: 'sessao',\n                    title: 'Sessão',\n                    icon: _barrel_optimize_names_Activity_Award_BarChart_Bell_BookOpen_Box_Brain_Briefcase_Building_Calculator_Calendar_ChevronDown_ChevronRight_ClipboardList_Clock_Construction_CreditCard_Database_DollarSign_FileText_Gift_GraduationCap_HardHat_Home_Info_LayoutDashboard_Lock_LogOut_MapPin_Menu_MessageCircle_Search_Settings_Shield_ShieldCheck_ShieldIcon_Tag_TrendingUp_UserCheck_UserCog_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                    description: 'Gerenciar sessões de atendimento'\n                }\n            ]\n        }\n    ]\n};\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Header\");\n$RefreshReg$(_c1, \"NavLink\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/components.js\n"));

/***/ })

});