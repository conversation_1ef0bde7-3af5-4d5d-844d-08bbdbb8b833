// Script para criar empresas de teste com assinaturas
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestCompanies() {
  try {
    // Verificar empresas existentes
    const existingCompanies = await prisma.company.findMany({
      where: {
        active: true
      },
      include: {
        subscription: true
      }
    });

    console.log('Empresas existentes:', existingCompanies.length);

    // Criar empresas de teste se necessário
    const testCompanies = [
      {
        name: '<PERSON>línica Saúde Total',
        cnpj: '11.222.333/0001-44',
        contactEmail: '<EMAIL>',
        phone: '(11) 3333-4444',
        subscription: {
          billingCycle: 'MONTHLY',
          pricePerMonth: 299.90,
          userLimit: 25,
          modules: ['BASIC', 'ADMIN', 'SCHEDULING', 'RH']
        }
      },
      {
        name: 'Centro Terapêutico Bem Estar',
        cnpj: '22.333.444/0001-55',
        contactEmail: '<EMAIL>',
        phone: '(11) 5555-6666',
        subscription: {
          billingCycle: 'YEARLY',
          pricePerMonth: 199.90,
          userLimit: 15,
          modules: ['BASIC', 'ADMIN', 'SCHEDULING']
        }
      },
      {
        name: 'Instituto de Reabilitação Avançada',
        cnpj: '33.444.555/0001-66',
        contactEmail: '<EMAIL>',
        phone: '(11) 7777-8888',
        subscription: {
          billingCycle: 'MONTHLY',
          pricePerMonth: 499.90,
          userLimit: 100,
          modules: ['BASIC', 'ADMIN', 'SCHEDULING', 'RH', 'FINANCIAL']
        }
      }
    ];

    for (const companyData of testCompanies) {
      // Verificar se a empresa já existe
      const existingCompany = existingCompanies.find(c => c.name === companyData.name);
      
      if (existingCompany) {
        console.log(`Empresa ${companyData.name} já existe, pulando...`);
        continue;
      }

      // Criar empresa
      const company = await prisma.company.create({
        data: {
          name: companyData.name,
          cnpj: companyData.cnpj,
          contactEmail: companyData.contactEmail,
          phone: companyData.phone,
          active: true
        }
      });

      console.log(`Empresa criada: ${company.name} (${company.id})`);

      // Criar assinatura
      const subscription = await prisma.subscription.create({
        data: {
          companyId: company.id,
          billingCycle: companyData.subscription.billingCycle,
          status: 'ACTIVE',
          pricePerMonth: companyData.subscription.pricePerMonth,
          userLimit: companyData.subscription.userLimit,
          startDate: new Date(),
          nextBillingDate: new Date(Date.now() + (companyData.subscription.billingCycle === 'YEARLY' ? 365 : 30) * 24 * 60 * 60 * 1000),
        }
      });

      console.log(`Assinatura criada para ${company.name}: ${subscription.id}`);

      // Criar módulos da assinatura
      for (const moduleType of companyData.subscription.modules) {
        const pricePerMonth = moduleType === 'BASIC' || moduleType === 'ADMIN' || moduleType === 'SCHEDULING' ? 0 : 
                             moduleType === 'RH' ? 49.90 : 
                             moduleType === 'FINANCIAL' ? 29.90 : 0;

        await prisma.subscriptionModule.create({
          data: {
            subscriptionId: subscription.id,
            moduleType: moduleType,
            pricePerMonth: pricePerMonth,
            active: true
          }
        });
      }

      console.log(`Módulos criados para ${company.name}: ${companyData.subscription.modules.join(', ')}`);
    }

    console.log('Empresas de teste criadas com sucesso!');

    // Listar todas as empresas com assinaturas
    const allCompanies = await prisma.company.findMany({
      where: {
        active: true
      },
      include: {
        subscription: {
          include: {
            modules: true
          }
        }
      }
    });

    console.log('\n=== RESUMO DAS EMPRESAS ===');
    allCompanies.forEach(company => {
      console.log(`\n${company.name}:`);
      if (company.subscription) {
        console.log(`  - Status: ${company.subscription.status}`);
        console.log(`  - Preço: R$ ${company.subscription.pricePerMonth}`);
        console.log(`  - Usuários: ${company.subscription.userLimit}`);
        console.log(`  - Módulos: ${company.subscription.modules.map(m => m.moduleType).join(', ')}`);
      } else {
        console.log('  - Sem assinatura');
      }
    });

  } catch (error) {
    console.error('Erro ao criar empresas de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestCompanies();
