"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/app/dashboard/ClientHeader.js":
/*!*******************************************!*\
  !*** ./src/app/dashboard/ClientHeader.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.js\");\n/* harmony import */ var _config_appConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/appConfig */ \"(app-pages-browser)/./src/config/appConfig.js\");\n/* harmony import */ var _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/people/services/personsService */ \"(app-pages-browser)/./src/app/modules/people/services/personsService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Header Component adaptado para clientes\nconst ClientHeader = (param)=>{\n    let { toggleSidebar, isSidebarOpen } = param;\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { openQuickNav } = (0,_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav)();\n    // Pegar primeira letra de cada nome para o avatar\n    const getInitials = ()=>{\n        var _user_login;\n        // Verificar se temos uma pessoa associada ao cliente\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            const fullName = user.persons[0].fullName;\n            const names = fullName.split(' ');\n            if (names.length === 1) return names[0].charAt(0);\n            return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n        }\n        // Fallback para o login do cliente\n        return (user === null || user === void 0 ? void 0 : (_user_login = user.login) === null || _user_login === void 0 ? void 0 : _user_login.charAt(0)) || 'C';\n    };\n    // Obter o nome completo da pessoa associada ao cliente ou o login do cliente\n    const getDisplayName = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            return user.persons[0].fullName;\n        }\n        return (user === null || user === void 0 ? void 0 : user.login) || 'Cliente';\n    };\n    // Obter a URL da imagem de perfil da pessoa associada ao cliente\n    const getProfileImage = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0) {\n            // Primeiro tenta usar a URL completa se disponível\n            if (user.persons[0].profileImageFullUrl) {\n                return user.persons[0].profileImageFullUrl;\n            } else if (user.persons[0].profileImageUrl) {\n                return _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_7__.personsService.getProfileImageUrl(user.persons[0].id, user.persons[0].profileImageUrl);\n            }\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9999]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors\",\n                        \"aria-label\": isSidebarOpen ? \"Fechar menu lateral\" : \"Abrir menu lateral\",\n                        children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 67,\n                            columnNumber: 28\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 67,\n                            columnNumber: 65\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo_horizontal_sem_fundo.png\",\n                                    alt: \"High Tide Logo\",\n                                    className: \"h-10 mr-2.5 dark:invert dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\",\n                                    children: _config_appConfig__WEBPACK_IMPORTED_MODULE_6__.APP_VERSION\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openQuickNav,\n                        className: \"flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors\",\n                        \"aria-label\": \"Abrir pesquisa r\\xe1pida\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 18,\n                                className: \"text-gray-400 dark:text-gray-500\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Pesquisar...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ctrl + K\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/calendar'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\",\n                        \"aria-label\": \"Calend\\xe1rio\",\n                        title: \"Ver calend\\xe1rio\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\",\n                        \"aria-label\": \"Meus Agendamentos\",\n                        title: \"Ver meus agendamentos\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 border-l border-gray-200 dark:border-gray-700 mx-1\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                \"aria-expanded\": \"false\",\n                                \"aria-haspopup\": \"true\",\n                                \"aria-label\": \"Menu do usu\\xe1rio\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-9 w-9 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center font-medium text-purple-600 dark:text-purple-400 overflow-hidden\",\n                                        children: getProfileImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: getProfileImage(),\n                                            alt: \"Foto de perfil de \".concat(getDisplayName()),\n                                            className: \"h-10 w-10 rounded-full object-cover\",\n                                            onError: (e)=>{\n                                                e.target.onerror = null;\n                                                e.target.style.display = 'none';\n                                                e.target.parentNode.innerHTML = getInitials();\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined) : getInitials()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 bg-purple-50 dark:bg-purple-900/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: 10,\n                                                        className: \"mr-1\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Cliente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-gray-400 dark:text-gray-500 hidden md:block\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right\",\n                                role: \"menu\",\n                                \"aria-orientation\": \"vertical\",\n                                \"aria-labelledby\": \"user-menu-button\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-1 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/profile'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meu Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/people/persons'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Minhas Pessoas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meus Agendamentos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: logout,\n                                                className: \"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors\",\n                                                role: \"menuitem\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"mr-2\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Sair do Sistema\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientHeader, \"lxBQFzzV6dgk+z4JHIb9l7reeX8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav\n    ];\n});\n_c = ClientHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientHeader);\nvar _c;\n$RefreshReg$(_c, \"ClientHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/ClientHeader.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/tutorial/ContextualHelpButton.js":
/*!*********************************************************!*\
  !*** ./src/components/tutorial/ContextualHelpButton.js ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HelpCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/TutorialContext */ \"(app-pages-browser)/./src/contexts/TutorialContext.js\");\n/* harmony import */ var _tutorials_tutorialMapping__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * Botão de ajuda contextual que mostra tutoriais específicos com base na rota atual\r\n */ const ContextualHelpButton = ()=>{\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { startTutorial, isActive } = (0,_contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_3__.useTutorial)();\n    const [currentTutorial, setCurrentTutorial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Busca o tutorial apropriado quando a rota muda\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContextualHelpButton.useEffect\": ()=>{\n            const tutorialData = (0,_tutorials_tutorialMapping__WEBPACK_IMPORTED_MODULE_4__.getTutorialForRoute)(pathname);\n            setCurrentTutorial(tutorialData);\n        }\n    }[\"ContextualHelpButton.useEffect\"], [\n        pathname\n    ]);\n    // Função para iniciar o tutorial contextual\n    const handleStartTutorial = ()=>{\n        if (currentTutorial && currentTutorial.steps && currentTutorial.steps.length > 0) {\n            startTutorial(currentTutorial.steps, currentTutorial.name);\n        } else {\n            // Se não temos um tutorial para esta página, podemos mostrar uma mensagem\n            console.log('Nenhum tutorial disponível para esta página');\n        // Você pode adicionar aqui uma notificação para o usuário\n        }\n    };\n    // Se o tutorial já estiver ativo, não mostramos o botão\n    if (isActive) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-6 right-6 z-50\",\n        children: [\n            isHovering && currentTutorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-16 right-0 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-3 mb-2 w-48 text-sm text-gray-700 dark:text-gray-300 animate-fade-in\",\n                children: [\n                    \"Clique para ver o tutorial desta p\\xe1gina\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-5 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white dark:bg-gray-800\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleStartTutorial,\n                onMouseEnter: ()=>setIsHovering(true),\n                onMouseLeave: ()=>setIsHovering(false),\n                className: \"\\n          w-12 h-12 rounded-full flex items-center justify-center shadow-lg \\n          transition-all duration-300 hover:shadow-xl\\n          \".concat(currentTutorial ? 'bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700' : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400', \"\\n        \"),\n                \"aria-label\": \"Mostrar tutorial da p\\xe1gina\",\n                disabled: !currentTutorial,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HelpCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        size: currentTutorial ? 28 : 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    currentTutorial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute -top-1 -right-1 h-3 w-3 rounded-full bg-white border-2 border-primary-500 dark:border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\tutorial\\\\ContextualHelpButton.js\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContextualHelpButton, \"gHs9ujTst09JuXRWcazRa6AU0b8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_TutorialContext__WEBPACK_IMPORTED_MODULE_3__.useTutorial\n    ];\n});\n_c = ContextualHelpButton;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContextualHelpButton);\nvar _c;\n$RefreshReg$(_c, \"ContextualHelpButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/tutorial/ContextualHelpButton.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/multi-select.js":
/*!*******************************************!*\
  !*** ./src/components/ui/multi-select.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Loader2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst MultiSelect = (param)=>{\n    let { label, options = [], value = [], onChange = ()=>{}, placeholder = \"Selecionar...\", disabled = false, loading = false, error = false, required = false, className = \"\", moduleOverride = null } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Montar o componente apenas no cliente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MultiSelect.useEffect\": ()=>{\n            setMounted(true);\n            return ({\n                \"MultiSelect.useEffect\": ()=>setMounted(false)\n            })[\"MultiSelect.useEffect\"];\n        }\n    }[\"MultiSelect.useEffect\"], []);\n    // Verificar se o dropdown deve ser exibido acima ou abaixo do select\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MultiSelect.useEffect\": ()=>{\n            if (!isOpen || !dropdownRef.current || !containerRef.current) return;\n            const checkPosition = {\n                \"MultiSelect.useEffect.checkPosition\": ()=>{\n                    const containerRect = containerRef.current.getBoundingClientRect();\n                    const dropdownHeight = dropdownRef.current.offsetHeight;\n                    const windowHeight = window.innerHeight;\n                    const spaceBelow = windowHeight - containerRect.bottom;\n                    // Se não houver espaço suficiente abaixo e houver mais espaço acima\n                    if (spaceBelow < dropdownHeight && containerRect.top > dropdownHeight) {\n                        dropdownRef.current.style.top = 'auto';\n                        dropdownRef.current.style.bottom = '100%';\n                        dropdownRef.current.style.marginTop = '0';\n                        dropdownRef.current.style.marginBottom = '4px';\n                    } else {\n                        dropdownRef.current.style.top = '100%';\n                        dropdownRef.current.style.bottom = 'auto';\n                        dropdownRef.current.style.marginTop = '4px';\n                        dropdownRef.current.style.marginBottom = '0';\n                    }\n                }\n            }[\"MultiSelect.useEffect.checkPosition\"];\n            // Verificar a posição quando o dropdown é aberto\n            checkPosition();\n            // Verificar novamente após um pequeno atraso para garantir que o dropdown foi renderizado corretamente\n            const timer = setTimeout(checkPosition, 50);\n            return ({\n                \"MultiSelect.useEffect\": ()=>clearTimeout(timer)\n            })[\"MultiSelect.useEffect\"];\n        }\n    }[\"MultiSelect.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MultiSelect.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"MultiSelect.useEffect.handleClickOutside\": (event)=>{\n                    if (containerRef.current && !containerRef.current.contains(event.target) && dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                        setSearch('');\n                        setIsFocused(false);\n                    }\n                }\n            }[\"MultiSelect.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"MultiSelect.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"MultiSelect.useEffect\"];\n        }\n    }[\"MultiSelect.useEffect\"], []);\n    // Log detalhado das opções recebidas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MultiSelect.useEffect\": ()=>{\n            if (label === \"Profissionais\") {\n                console.log(\"[MultiSelect-\".concat(label, \"] Op\\xe7\\xf5es recebidas:\"), options);\n                console.log(\"[MultiSelect-\".concat(label, \"] Valores selecionados:\"), value);\n            }\n        }\n    }[\"MultiSelect.useEffect\"], [\n        options,\n        value,\n        label\n    ]);\n    // Garantir que as opções sejam sempre um array\n    const safeOptions = Array.isArray(options) ? options : [];\n    // Filtrar opções com base na busca\n    const filteredOptions = safeOptions.filter((option)=>{\n        // Verificar se a opção é válida\n        if (!option || typeof option !== 'object' || !option.label) {\n            if (label === \"Profissionais\") {\n                console.warn(\"[MultiSelect-\".concat(label, \"] Op\\xe7\\xe3o inv\\xe1lida encontrada:\"), option);\n            }\n            return false;\n        }\n        // Verificar se o label é uma string\n        const optionLabel = String(option.label || '');\n        const searchTerm = String(search || '').toLowerCase();\n        return optionLabel.toLowerCase().includes(searchTerm);\n    });\n    const handleRemoveItem = (itemValue)=>{\n        if (!disabled && onChange) {\n            onChange(value.filter((v)=>v !== itemValue));\n        }\n    };\n    const handleSelectItem = (itemValue)=>{\n        if (disabled || !onChange) return;\n        let newValue;\n        if (value.includes(itemValue)) {\n            newValue = value.filter((v)=>v !== itemValue);\n        } else {\n            newValue = [\n                ...value,\n                itemValue\n            ];\n        }\n        console.log(\"MultiSelect - Valor alterado:\", {\n            anterior: value,\n            novo: newValue,\n            itemSelecionado: itemValue,\n            tipoItem: typeof itemValue\n        });\n        onChange(newValue);\n    };\n    const handleToggleDropdown = (e)=>{\n        e.stopPropagation();\n        if (!disabled) {\n            setIsOpen(!isOpen);\n            if (!isOpen) {\n                var _inputRef_current;\n                setSearch('');\n                (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n            }\n        }\n    };\n    const getContainerStyles = ()=>{\n        const baseStyles = \"relative w-full\";\n        const stateStyles = disabled ? \"opacity-50 cursor-not-allowed\" : \"cursor-pointer\";\n        return \"\".concat(baseStyles, \" \").concat(stateStyles, \" \").concat(className);\n    };\n    const getInputContainerStyles = ()=>{\n        const baseStyles = \"min-h-[42px] px-2 py-1.5 border rounded-lg transition-all duration-200 flex flex-wrap gap-1.5 items-center bg-white dark:bg-gray-700\";\n        // Background styles já incluídos no baseStyles\n        // Focus styles based on module\n        let focusStyles = '';\n        if (isFocused) {\n            if (moduleOverride === 'scheduler') {\n                focusStyles = \"ring-2 ring-module-scheduler-border dark:ring-module-scheduler-border-dark border-module-scheduler-border dark:border-module-scheduler-border-dark\";\n            } else if (moduleOverride === 'people') {\n                focusStyles = \"ring-2 ring-module-people-border dark:ring-module-people-border-dark border-module-people-border dark:border-module-people-border-dark\";\n            } else if (moduleOverride === 'admin') {\n                focusStyles = \"ring-2 ring-slate-300 dark:ring-slate-600 border-slate-400 dark:border-slate-500\";\n            } else {\n                focusStyles = \"ring-2 ring-primary-200 border-primary-300\";\n            }\n        } else {\n            if (moduleOverride === 'scheduler') {\n                focusStyles = \"hover:border-module-scheduler-border dark:hover:border-module-scheduler-border-dark\";\n            } else if (moduleOverride === 'people') {\n                focusStyles = \"hover:border-module-people-border dark:hover:border-module-people-border-dark\";\n            } else if (moduleOverride === 'admin') {\n                focusStyles = \"hover:border-slate-400 dark:hover:border-slate-500\";\n            } else {\n                focusStyles = \"hover:border-neutral-400\";\n            }\n        }\n        // Error styles based on module\n        let errorStyles = '';\n        if (error) {\n            errorStyles = \"border-error-500 hover:border-error-500\";\n        } else if (moduleOverride === 'scheduler') {\n            errorStyles = \"border-module-scheduler-border dark:border-module-scheduler-border-dark\";\n        } else if (moduleOverride === 'people') {\n            errorStyles = \"border-module-people-border dark:border-module-people-border-dark\";\n        } else {\n            errorStyles = \"border-neutral-300 dark:border-gray-600\";\n        }\n        return \"\".concat(baseStyles, \" \").concat(focusStyles, \" \").concat(errorStyles);\n    };\n    const getTagStyles = ()=>{\n        if (moduleOverride === 'scheduler') {\n            return \"bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark text-module-scheduler-text dark:text-module-scheduler-text-dark text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark\";\n        } else if (moduleOverride === 'people') {\n            return \"bg-module-people-bg dark:bg-module-people-bg-dark text-module-people-text dark:text-module-people-text-dark text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-module-people-hover dark:hover:bg-module-people-hover-dark\";\n        } else if (moduleOverride === 'admin') {\n            return \"bg-slate-200 dark:bg-slate-600 text-slate-800 dark:text-slate-100 text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-slate-300 dark:hover:bg-slate-500\";\n        } else {\n            return \"bg-primary-50 text-primary-700 text-sm px-2 py-1 rounded-md flex items-center gap-1.5 group transition-colors duration-200 hover:bg-primary-100\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(getContainerStyles(), \" overflow-visible\"),\n        ref: containerRef,\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"flex gap-1 text-sm font-medium mb-1.5 \".concat(moduleOverride === 'scheduler' ? 'text-module-scheduler-text dark:text-module-scheduler-text-dark' : moduleOverride === 'people' ? 'text-module-people-text dark:text-module-people-text-dark' : moduleOverride === 'admin' ? 'text-slate-700 dark:text-slate-300' : 'text-neutral-700 dark:text-gray-300'),\n                children: [\n                    label,\n                    required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-error-500\",\n                        children: \"*\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                        lineNumber: 224,\n                        columnNumber: 24\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: getInputContainerStyles(),\n                onClick: handleToggleDropdown,\n                onFocus: ()=>setIsFocused(true),\n                onBlur: ()=>!isOpen && setIsFocused(false),\n                tabIndex: 0,\n                role: \"combobox\",\n                \"aria-expanded\": isOpen,\n                \"aria-haspopup\": \"listbox\",\n                \"aria-disabled\": disabled,\n                children: [\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-4 h-4 text-neutral-400 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, undefined) : (value === null || value === void 0 ? void 0 : value.length) > 0 ? value.map((v)=>{\n                        const option = options === null || options === void 0 ? void 0 : options.find((opt)=>(opt === null || opt === void 0 ? void 0 : opt.value) === v);\n                        return option ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: getTagStyles(),\n                            children: [\n                                option.label,\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    size: 14,\n                                    className: \"\".concat(moduleOverride === 'admin' ? 'text-slate-700 dark:text-slate-200' : 'text-primary-600', \" opacity-60 group-hover:opacity-100 cursor-pointer\"),\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleRemoveItem(v);\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, v, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                            lineNumber: 245,\n                            columnNumber: 15\n                        }, undefined) : null;\n                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"py-0.5 \".concat(moduleOverride === 'scheduler' ? 'text-module-scheduler-text/50 dark:text-module-scheduler-text-dark/50' : moduleOverride === 'people' ? 'text-module-people-text/50 dark:text-module-people-text-dark/50' : moduleOverride === 'admin' ? 'text-neutral-500 dark:text-gray-400' : 'text-neutral-400'),\n                        children: placeholder\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                        lineNumber: 263,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        size: 18,\n                        className: \"ml-auto transition-transform duration-200 \".concat(isOpen ? 'transform rotate-180' : '', \" \").concat(moduleOverride === 'scheduler' ? 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark' : moduleOverride === 'people' ? 'text-module-people-icon dark:text-module-people-icon-dark' : moduleOverride === 'admin' ? 'text-slate-500 dark:text-slate-400' : 'text-neutral-400')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-error-500\",\n                children: typeof error === 'string' ? error : 'Este campo é obrigatório'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, undefined),\n            isOpen && mounted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: dropdownRef,\n                className: \"absolute z-50 w-full rounded-lg shadow-lg animate-in fade-in-0 zoom-in-95 \".concat(moduleOverride === 'scheduler' ? 'bg-white dark:bg-gray-800 border border-module-scheduler-border dark:border-module-scheduler-border-dark' : moduleOverride === 'people' ? 'bg-white dark:bg-gray-800 border border-module-people-border dark:border-module-people-border-dark' : moduleOverride === 'admin' ? 'bg-white dark:bg-gray-800 border border-neutral-200 dark:border-gray-600' : 'bg-white border border-neutral-200'),\n                style: {\n                    top: '100%',\n                    left: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 \".concat(moduleOverride === 'scheduler' ? 'border-b border-module-scheduler-border/30 dark:border-module-scheduler-border-dark/30' : moduleOverride === 'people' ? 'border-b border-module-people-border/30 dark:border-module-people-border-dark/30' : moduleOverride === 'admin' ? 'border-b border-neutral-100 dark:border-gray-700' : 'border-b border-neutral-100'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: inputRef,\n                                type: \"text\",\n                                className: \"w-full pl-2 pr-8 py-1.5 text-sm rounded-md focus:outline-none \".concat(moduleOverride === 'scheduler' ? 'border border-module-scheduler-border dark:border-module-scheduler-border-dark bg-neutral-50 dark:bg-gray-700 placeholder-module-scheduler-text/50 dark:placeholder-module-scheduler-text-dark/50 text-module-scheduler-text dark:text-module-scheduler-text-dark focus:ring-2 focus:ring-module-scheduler-border dark:focus:ring-module-scheduler-border-dark focus:border-module-scheduler-border dark:focus:border-module-scheduler-border-dark' : moduleOverride === 'people' ? 'border border-module-people-border dark:border-module-people-border-dark bg-neutral-50 dark:bg-gray-700 placeholder-module-people-text/50 dark:placeholder-module-people-text-dark/50 text-module-people-text dark:text-module-people-text-dark focus:ring-2 focus:ring-module-people-border dark:focus:ring-module-people-border-dark focus:border-module-people-border dark:focus:border-module-people-border-dark' : moduleOverride === 'admin' ? 'border border-neutral-200 dark:border-gray-600 bg-neutral-50 dark:bg-gray-700 placeholder-neutral-400 dark:placeholder-gray-400 text-neutral-800 dark:text-gray-200 focus:ring-2 focus:ring-slate-300 dark:focus:ring-slate-600 focus:border-slate-400 dark:focus:border-slate-500' : 'border border-neutral-200 bg-neutral-50 placeholder-neutral-400 focus:ring-2 focus:ring-primary-200 focus:border-primary-300'),\n                                placeholder: \"Buscar...\",\n                                value: search,\n                                onChange: (e)=>setSearch(e.target.value),\n                                onClick: (e)=>e.stopPropagation()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                lineNumber: 322,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-60 overflow-auto\",\n                        children: filteredOptions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-3 text-sm text-center \".concat(moduleOverride === 'scheduler' ? 'text-module-scheduler-text/70 dark:text-module-scheduler-text-dark/70' : moduleOverride === 'people' ? 'text-module-people-text/70 dark:text-module-people-text-dark/70' : moduleOverride === 'admin' ? 'text-neutral-500 dark:text-gray-400' : 'text-neutral-500'),\n                            children: label === \"Profissionais\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Nenhum profissional encontrado\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                        lineNumber: 355,\n                                        columnNumber: 21\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"mt-2 px-3 py-1 rounded-md \".concat(moduleOverride === 'scheduler' ? 'bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark text-module-scheduler-text dark:text-module-scheduler-text-dark hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark' : moduleOverride === 'people' ? 'bg-module-people-bg dark:bg-module-people-bg-dark text-module-people-text dark:text-module-people-text-dark hover:bg-module-people-hover dark:hover:bg-module-people-hover-dark' : moduleOverride === 'admin' ? 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-slate-200 dark:hover:bg-slate-600' : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200'),\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            console.log(\"[MultiSelect] Forçando recarregamento de dados...\");\n                                            // Adicionar alguns itens de teste\n                                            const testOptions = [\n                                                {\n                                                    value: \"test-provider-1\",\n                                                    label: \"Dr. Teste 1\"\n                                                },\n                                                {\n                                                    value: \"test-provider-2\",\n                                                    label: \"Dr. Teste 2\"\n                                                },\n                                                {\n                                                    value: \"test-provider-3\",\n                                                    label: \"Dr. Teste 3\"\n                                                }\n                                            ];\n                                            // Atualizar as opções diretamente no componente pai\n                                            if (typeof onChange === 'function') {\n                                                onChange([]);\n                                            }\n                                        },\n                                        children: \"Recarregar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 21\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                lineNumber: 354,\n                                columnNumber: 19\n                            }, undefined) || \"Nenhum resultado encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                            lineNumber: 344,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-1\",\n                            children: filteredOptions.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"\\n                      flex items-center justify-between px-3 py-2 text-sm cursor-pointer\\n                      transition-colors duration-150\\n                      \".concat(value.includes(option.value) ? moduleOverride === 'scheduler' ? 'bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark text-module-scheduler-text dark:text-module-scheduler-text-dark' : moduleOverride === 'people' ? 'bg-module-people-bg dark:bg-module-people-bg-dark text-module-people-text dark:text-module-people-text-dark' : moduleOverride === 'admin' ? 'bg-slate-200 dark:bg-slate-600 text-slate-800 dark:text-slate-100' : 'bg-primary-50 text-primary-700' : moduleOverride === 'scheduler' ? 'text-module-scheduler-text dark:text-module-scheduler-text-dark hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark' : moduleOverride === 'people' ? 'text-module-people-text dark:text-module-people-text-dark hover:bg-module-people-hover dark:hover:bg-module-people-hover-dark' : moduleOverride === 'admin' ? 'text-slate-800 dark:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-700' : 'text-neutral-700 hover:bg-neutral-50', \"\\n                    \"),\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleSelectItem(option.value);\n                                    },\n                                    role: \"option\",\n                                    \"aria-selected\": value.includes(option.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: option.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                            lineNumber: 417,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        value.includes(option.value) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Loader2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 16,\n                                            className: moduleOverride === 'scheduler' ? 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark' : moduleOverride === 'people' ? 'text-module-people-icon dark:text-module-people-icon-dark' : moduleOverride === 'admin' ? 'text-slate-700 dark:text-slate-200' : 'text-primary-500'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                            lineNumber: 419,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, option.value || \"option-\".concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                                    lineNumber: 389,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                            lineNumber: 387,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\multi-select.js\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MultiSelect, \"U+QHQypUbmidpmUssrEDsLIc5fY=\");\n_c = MultiSelect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultiSelect);\nvar _c;\n$RefreshReg$(_c, \"MultiSelect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/multi-select.js\n"));

/***/ })

});