'use client';

import React, { useState, useEffect } from 'react';
import { X, Loader2, Building, MapPin, Phone, Globe, Mail, Upload, Trash } from 'lucide-react';
import { companyService } from '@/app/modules/admin/services/companyService';
import { companyLogoService } from '@/app/modules/admin/services/companyLogoService';

// Componente para input com máscara simples
const SimpleMaskedInput = ({ name, value, onChange, mask, placeholder, className, disabled }) => {
  const formatWithMask = (val, mask) => {
    if (!val) return '';

    let formatted = '';
    let valIndex = 0;

    for (let i = 0; i < mask.length && valIndex < val.length; i++) {
      if (mask[i] === '9') {
        formatted += val[valIndex++] || '';
      } else {
        formatted += mask[i];
        // Skip character in value if it matches the mask
        if (val[valIndex] === mask[i]) valIndex++;
      }
    }

    return formatted;
  };

  const handleChange = (e) => {
    // Extract only digits
    const digits = e.target.value.replace(/\D/g, '');

    // Apply mask
    const formattedValue = formatWithMask(digits, mask);

    onChange({
      target: {
        name,
        value: formattedValue
      }
    });
  };

  return (
    <input
      type="text"
      name={name}
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
    />
  );
};

const CompanyFormModal = ({ isOpen, onClose, company, onSuccess }) => {
  // Proteção contra renderização no servidor
  if (typeof window === 'undefined') return null;
  if (!isOpen) return null;

  const [formData, setFormData] = useState({
    name: '',
    tradingName: '',
    cnpj: '',
    phone: '',
    phone2: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    website: '',
    primaryColor: '#FF9933', // Cor principal do sistema
    secondaryColor: '#3B82F6', // Cor secundária
    description: '',
    email: '',
    logo: null
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [logoPreview, setLogoPreview] = useState(null);

  // Função para processar campos JSON que podem vir como string
  const parseJsonField = (jsonData, fieldName = 'campo') => {
    if (!jsonData) return {};

    // Se já for um objeto, retornar como está
    if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {
      return jsonData;
    }

    // Se for uma string, tentar fazer o parse
    if (typeof jsonData === 'string') {
      try {
        return JSON.parse(jsonData);
      } catch (error) {
        console.error(`[CompanyFormModal] Erro ao fazer parse do ${fieldName}:`, error);
        return {};
      }
    }

    return {};
  };

  // Carregar dados da empresa para edição
  useEffect(() => {
    if (company) {
      // Extrair redes sociais do objeto socialMedia
      const socialMedia = parseJsonField(company.socialMedia, 'socialMedia');
      console.log('[CompanyFormModal] socialMedia processado:', socialMedia);

      setFormData({
        name: company.name || '',
        tradingName: company.tradingName || '',
        cnpj: company.cnpj || '',
        phone: company.phone || '',
        phone2: company.phone2 || '',
        address: company.address || '',
        city: company.city || '',
        state: company.state || '',
        postalCode: company.postalCode || '',
        website: company.website || '',
        primaryColor: company.primaryColor || '#FF9933',
        secondaryColor: company.secondaryColor || '#3B82F6',
        description: company.description || '',
        email: company.email || '',
        facebook: socialMedia.facebook || '',
        instagram: socialMedia.instagram || '',
        linkedin: socialMedia.linkedin || '',
        twitter: socialMedia.twitter || '',
        youtube: socialMedia.youtube || '',
        logo: null
      });

      // Se a empresa tem um logo, definir a pré-visualização
      if (company.documents && company.documents[0]) {
        const logoUrl = companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path);
        setLogoPreview(logoUrl);
      }
    } else {
      // Reset do formulário para nova empresa
      resetForm();
    }
  }, [company, isOpen]);

  const resetForm = () => {
    setFormData({
      name: '',
      tradingName: '',
      cnpj: '',
      phone: '',
      phone2: '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      website: '',
      primaryColor: '#FF9933',
      secondaryColor: '#3B82F6',
      description: '',
      email: '',
      facebook: '',
      instagram: '',
      linkedin: '',
      twitter: '',
      youtube: '',
      logo: null
    });
    setLogoPreview(null);
    setErrors({});
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name) {
      newErrors.name = 'O nome da empresa é obrigatório';
    }

    if (formData.cnpj && formData.cnpj.replace(/\D/g, '').length !== 14) {
      newErrors.cnpj = 'CNPJ inválido';
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (formData.website && !/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/.test(formData.website)) {
      newErrors.website = 'Website inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Limpar erro ao editar o campo
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Verificar tipo de arquivo (apenas imagens)
    if (!file.type.startsWith('image/')) {
      setErrors((prev) => ({
        ...prev,
        logo: 'Por favor, selecione uma imagem válida'
      }));
      return;
    }

    // Limitar tamanho (2MB)
    if (file.size > 2 * 1024 * 1024) {
      setErrors((prev) => ({
        ...prev,
        logo: 'A imagem deve ter no máximo 2MB'
      }));
      return;
    }

    // Criar uma URL para pré-visualização
    const objectUrl = URL.createObjectURL(file);
    setLogoPreview(objectUrl);

    setFormData((prev) => ({ ...prev, logo: file }));

    // Limpar erro, se houver
    if (errors.logo) {
      setErrors((prev) => ({ ...prev, logo: undefined }));
    }
  };

  const removeLogo = () => {
    setFormData((prev) => ({ ...prev, logo: null }));
    setLogoPreview(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // Criar FormData para envio de arquivos
      const formDataToSend = new FormData();

      // Adicionar todos os campos do formulário
      Object.keys(formData).forEach(key => {
        if (key === 'logo') {
          if (formData.logo) {
            formDataToSend.append('logo', formData.logo);
          }
        } else if (formData[key] !== null && formData[key] !== '') {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Adicionar campos JSON para redes sociais
      const socialMedia = {
        facebook: formData.facebook || '',
        instagram: formData.instagram || '',
        linkedin: formData.linkedin || '',
        twitter: formData.twitter || '',
        youtube: formData.youtube || ''
      };

      formDataToSend.append('socialMedia', JSON.stringify(socialMedia));

      if (company) {
        // Atualizar empresa existente
        await companyService.updateCompany(company.id, formDataToSend);
      } else {
        // Criar nova empresa
        await companyService.createCompany(formDataToSend);
      }

      onSuccess();
    } catch (error) {
      console.error('Erro ao salvar empresa:', error);

      // Tratar erros da API
      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.param] = err.msg;
        });
        setErrors(apiErrors);
      } else {
        setErrors({
          submit: error.response?.data?.message || 'Erro ao salvar empresa'
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const inputClasses = "block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 text-neutral-900 dark:text-gray-100 dark:bg-gray-700";
  const labelClasses = "block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1";
  const errorClasses = "mt-1 text-xs text-red-600 dark:text-red-400";

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[55]">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2">
            <Building className="h-5 w-5 text-primary-500 dark:text-primary-400" />
            {company ? 'Editar Empresa' : 'Nova Empresa'}
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        {/* Formulário */}
        <div className="overflow-y-auto p-6">
          <form onSubmit={handleSubmit}>
            {errors.submit && (
              <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg">
                {errors.submit}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Informações básicas */}
              <div className="md:col-span-2">
                <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                  <Building className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                  Informações Básicas
                </h4>
              </div>

              {/* Nome */}
              <div>
                <label className={labelClasses} htmlFor="name">
                  Nome da Empresa *
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  value={formData.name}
                  onChange={handleChange}
                  className={`${inputClasses} ${errors.name ? "border-red-500 dark:border-red-700" : ""}`}
                  placeholder="Nome oficial da empresa"
                  disabled={isLoading}
                />
                {errors.name && <p className={errorClasses}>{errors.name}</p>}
              </div>

              {/* Nome Fantasia */}
              <div>
                <label className={labelClasses} htmlFor="tradingName">
                  Nome Fantasia
                </label>
                <input
                  id="tradingName"
                  name="tradingName"
                  type="text"
                  value={formData.tradingName}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Nome comercial ou fantasia"
                  disabled={isLoading}
                />
              </div>

              {/* CNPJ */}
              <div>
                <label className={labelClasses} htmlFor="cnpj">
                  CNPJ
                </label>
                <SimpleMaskedInput
                  name="cnpj"
                  mask="99.999.999/9999-99"
                  value={formData.cnpj}
                  onChange={handleChange}
                  placeholder="00.000.000/0000-00"
                  className={`${inputClasses} ${errors.cnpj ? "border-red-500 dark:border-red-700" : ""}`}
                  disabled={isLoading}
                />
                {errors.cnpj && <p className={errorClasses}>{errors.cnpj}</p>}
              </div>

              {/* Email */}
              <div>
                <label className={labelClasses} htmlFor="email">
                  Email
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    className={`${inputClasses} pl-10 ${errors.email ? "border-red-500 dark:border-red-700" : ""}`}
                    placeholder="<EMAIL>"
                    disabled={isLoading}
                  />
                </div>
                {errors.email && <p className={errorClasses}>{errors.email}</p>}
              </div>

              {/* Endereço */}
              <div className="md:col-span-2">
                <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                  Endereço
                </h4>
              </div>

              {/* Endereço */}
              <div className="md:col-span-2">
                <label className={labelClasses} htmlFor="address">
                  Endereço
                </label>
                <input
                  id="address"
                  name="address"
                  type="text"
                  value={formData.address}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Rua, número, complemento"
                  disabled={isLoading}
                />
              </div>

              {/* Cidade */}
              <div>
                <label className={labelClasses} htmlFor="city">
                  Cidade
                </label>
                <input
                  id="city"
                  name="city"
                  type="text"
                  value={formData.city}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Cidade"
                  disabled={isLoading}
                />
              </div>

              {/* Estado */}
              <div>
                <label className={labelClasses} htmlFor="state">
                  Estado
                </label>
                <input
                  id="state"
                  name="state"
                  type="text"
                  value={formData.state}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Estado"
                  disabled={isLoading}
                />
              </div>

              {/* CEP */}
              <div>
                <label className={labelClasses} htmlFor="postalCode">
                  CEP
                </label>
                <SimpleMaskedInput
                  name="postalCode"
                  mask="99999-999"
                  value={formData.postalCode}
                  onChange={handleChange}
                  placeholder="00000-000"
                  className={inputClasses}
                  disabled={isLoading}
                />
              </div>

              {/* Telefone */}
              <div>
                <label className={labelClasses} htmlFor="phone">
                  Telefone
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <SimpleMaskedInput
                    name="phone"
                    mask="(99) 99999-9999"
                    value={formData.phone}
                    onChange={handleChange}
                    placeholder="(00) 00000-0000"
                    className={`${inputClasses} pl-10`}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Website */}
              <div>
                <label className={labelClasses} htmlFor="website">
                  Website
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="website"
                    name="website"
                    type="url"
                    value={formData.website}
                    onChange={handleChange}
                    className={`${inputClasses} pl-10 ${errors.website ? "border-red-500 dark:border-red-700" : ""}`}
                    placeholder="https://www.empresa.com.br"
                    disabled={isLoading}
                  />
                </div>
                {errors.website && <p className={errorClasses}>{errors.website}</p>}
              </div>

              {/* Telefone Secundário */}
              <div>
                <label className={labelClasses} htmlFor="phone2">
                  Telefone Secundário
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <SimpleMaskedInput
                    name="phone2"
                    mask="(99) 99999-9999"
                    value={formData.phone2}
                    onChange={handleChange}
                    placeholder="(00) 00000-0000"
                    className={`${inputClasses} pl-10`}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Cores */}
              <div className="md:col-span-2">
                <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                  <span className="h-4 w-4 rounded-full bg-primary-500 dark:bg-primary-400"></span>
                  Cores e Identidade Visual
                </h4>
              </div>

              {/* Cor Primária */}
              <div>
                <label className={labelClasses} htmlFor="primaryColor">
                  Cor Primária
                </label>
                <div className="flex gap-2 items-center">
                  <input
                    id="primaryColor"
                    name="primaryColor"
                    type="color"
                    value={formData.primaryColor}
                    onChange={handleChange}
                    className="w-10 h-10 rounded-md"
                    disabled={isLoading}
                  />
                  <input
                    type="text"
                    value={formData.primaryColor}
                    onChange={handleChange}
                    name="primaryColor"
                    className={inputClasses}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Cor Secundária */}
              <div>
                <label className={labelClasses} htmlFor="secondaryColor">
                  Cor Secundária
                </label>
                <div className="flex gap-2 items-center">
                  <input
                    id="secondaryColor"
                    name="secondaryColor"
                    type="color"
                    value={formData.secondaryColor}
                    onChange={handleChange}
                    className="w-10 h-10 rounded-md"
                    disabled={isLoading}
                  />
                  <input
                    type="text"
                    value={formData.secondaryColor}
                    onChange={handleChange}
                    name="secondaryColor"
                    className={inputClasses}
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Logo */}
              <div className="md:col-span-2">
                <label className={labelClasses}>
                  Logo da Empresa
                </label>
                <div className="mt-1 flex items-center gap-4">
                  {logoPreview ? (
                    <div className="relative group">
                      <img
                        src={logoPreview}
                        alt="Logo preview"
                        className="h-24 w-24 object-contain border border-neutral-300 dark:border-gray-600 rounded-md"
                      />
                      <button
                        type="button"
                        onClick={removeLogo}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Trash className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="h-24 w-24 border-2 border-dashed border-neutral-300 dark:border-gray-600 rounded-md flex items-center justify-center text-neutral-400 dark:text-gray-500">
                      <Building className="h-10 w-10" />
                    </div>
                  )}

                  <div>
                    <label
                      htmlFor="logo-upload"
                      className="cursor-pointer px-4 py-2 bg-neutral-100 dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-md text-neutral-700 dark:text-gray-200 hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      <span>Fazer upload</span>
                      <input
                        id="logo-upload"
                        type="file"
                        accept="image/*"
                        onChange={handleFileChange}
                        className="hidden"
                        disabled={isLoading}
                      />
                    </label>
                    <p className="mt-1 text-xs text-neutral-500 dark:text-gray-400">
                      JPG, PNG ou GIF. Máximo 2MB.
                    </p>
                  </div>
                </div>
                {errors.logo && <p className={errorClasses}>{errors.logo}</p>}
              </div>

              {/* Redes Sociais */}
              <div className="md:col-span-2">
                <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                  <Globe className="h-4 w-4 text-primary-500 dark:text-primary-400" />
                  Redes Sociais
                </h4>
              </div>

              {/* Facebook */}
              <div>
                <label className={labelClasses} htmlFor="facebook">
                  Facebook
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="facebook"
                    name="facebook"
                    type="text"
                    value={formData.facebook}
                    onChange={handleChange}
                    className={`${inputClasses} pl-10`}
                    placeholder="URL do Facebook"
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Instagram */}
              <div>
                <label className={labelClasses} htmlFor="instagram">
                  Instagram
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="instagram"
                    name="instagram"
                    type="text"
                    value={formData.instagram}
                    onChange={handleChange}
                    className={`${inputClasses} pl-10`}
                    placeholder="URL do Instagram"
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* LinkedIn */}
              <div>
                <label className={labelClasses} htmlFor="linkedin">
                  LinkedIn
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="linkedin"
                    name="linkedin"
                    type="text"
                    value={formData.linkedin}
                    onChange={handleChange}
                    className={`${inputClasses} pl-10`}
                    placeholder="URL do LinkedIn"
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Twitter */}
              <div>
                <label className={labelClasses} htmlFor="twitter">
                  Twitter
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="twitter"
                    name="twitter"
                    type="text"
                    value={formData.twitter}
                    onChange={handleChange}
                    className={`${inputClasses} pl-10`}
                    placeholder="URL do Twitter"
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* YouTube */}
              <div>
                <label className={labelClasses} htmlFor="youtube">
                  YouTube
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Globe className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  </div>
                  <input
                    id="youtube"
                    name="youtube"
                    type="text"
                    value={formData.youtube}
                    onChange={handleChange}
                    className={`${inputClasses} pl-10`}
                    placeholder="URL do YouTube"
                    disabled={isLoading}
                  />
                </div>
              </div>

              {/* Descrição */}
              <div className="md:col-span-2">
                <label className={labelClasses} htmlFor="description">
                  Descrição da Empresa
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows="4"
                  className={inputClasses}
                  placeholder="Breve descrição sobre a empresa"
                  disabled={isLoading}
                ></textarea>
              </div>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 size={16} className="animate-spin" />
                <span>Salvando...</span>
              </>
            ) : (
              <span>Salvar</span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompanyFormModal;