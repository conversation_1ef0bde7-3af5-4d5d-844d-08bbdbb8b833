"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/introduction/page",{

/***/ "(app-pages-browser)/./src/components/users/ModulesModal.js":
/*!**********************************************!*\
  !*** ./src/components/users/ModulesModal.js ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Loader2,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ModulesModal = (param)=>{\n    let { isOpen, onClose, user, onSuccess } = param;\n    var _currentUser_modules;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [selectedModules, setSelectedModules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Configuração dos módulos disponíveis\n    const modules = [\n        {\n            id: \"ADMIN\",\n            name: \"Administração\",\n            description: \"Acesso completo ao sistema, incluindo configurações e gerenciamento de usuários\",\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"bg-slate-100 dark:bg-slate-900/30 text-slate-700 dark:text-slate-400 border-slate-200 dark:border-slate-800/50\",\n            requiresAdmin: true\n        },\n        {\n            id: \"RH\",\n            name: \"Recursos Humanos\",\n            description: \"Gerenciamento de funcionários, folha de pagamento e gestão de benefícios\",\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50\",\n            requiresAdmin: false\n        },\n        {\n            id: \"FINANCIAL\",\n            name: \"Financeiro\",\n            description: \"Controle de faturas, pagamentos, despesas e relatórios financeiros\",\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50\",\n            requiresAdmin: false\n        },\n        {\n            id: \"SCHEDULING\",\n            name: \"Agendamento\",\n            description: \"Gerenciamento de compromissos, reuniões e alocação de recursos\",\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50\",\n            requiresAdmin: false\n        },\n        {\n            id: \"BASIC\",\n            name: \"Básico\",\n            description: \"Acesso básico ao sistema, visualização limitada\",\n            icon: _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600\",\n            requiresAdmin: false,\n            disabled: true // Este módulo é obrigatório para todos os usuários\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModulesModal.useEffect\": ()=>{\n            if (user && isOpen) {\n                setSelectedModules(user.modules || []);\n            }\n        }\n    }[\"ModulesModal.useEffect\"], [\n        user,\n        isOpen\n    ]);\n    const isAdmin = currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_modules = currentUser.modules) === null || _currentUser_modules === void 0 ? void 0 : _currentUser_modules.includes(\"ADMIN\");\n    const handleToggleModule = (moduleId)=>{\n        if (moduleId === \"BASIC\") return; // Não pode remover o módulo básico\n        setSelectedModules((prev)=>{\n            if (prev.includes(moduleId)) {\n                return prev.filter((m)=>m !== moduleId);\n            } else {\n                return [\n                    ...prev,\n                    moduleId\n                ];\n            }\n        });\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            // Garante que o módulo BASIC sempre esteja presente\n            const modulesToSave = selectedModules.includes(\"BASIC\") ? selectedModules : [\n                ...selectedModules,\n                \"BASIC\"\n            ];\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__.userService.updateModules(user.id, modulesToSave);\n            onSuccess();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao atualizar módulos:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao atualizar módulos\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-2xl w-full max-h-[90vh] flex flex-col z-[11050]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-neutral-800 dark:text-white\",\n                                        children: \"Gerenciar M\\xf3dulos de Acesso\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto p-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-neutral-800 dark:text-white mb-1\",\n                                        children: user === null || user === void 0 ? void 0 : user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-600 dark:text-gray-300\",\n                                        children: \"Selecione os m\\xf3dulos que este usu\\xe1rio ter\\xe1 acesso:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: modules.map((module)=>{\n                                    const isSelected = selectedModules.includes(module.id);\n                                    const Icon = module.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 rounded-lg border \".concat(isSelected ? module.color : \"border-neutral-200 dark:border-gray-700\", \" \").concat(module.disabled ? \"opacity-70\" : \"cursor-pointer hover:border-primary-300 dark:hover:border-primary-700\"),\n                                        onClick: ()=>{\n                                            if (!module.disabled) {\n                                                handleToggleModule(module.id);\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: isSelected,\n                                                        onChange: ()=>{},\n                                                        disabled: module.disabled || module.requiresAdmin && !isAdmin,\n                                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 \".concat(isSelected ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                                    children: [\n                                                                        module.name,\n                                                                        module.id === \"ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded\",\n                                                                            children: \"Acesso Administrativo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                                            children: module.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        module.requiresAdmin && !isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    size: 12\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Apenas administradores podem conceder este acesso\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        module.id === \"BASIC\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    size: 12\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"M\\xf3dulo obrigat\\xf3rio para todos os usu\\xe1rios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, module.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onClose,\n                                className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                disabled: isLoading,\n                                children: \"Cancelar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSave,\n                                className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\",\n                                disabled: isLoading,\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 16,\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Salvando...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Loader2_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Salvar Permiss\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\ModulesModal.js\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModulesModal, \"5Lpn3KAQKXOdhkBw2mVP14nBRC8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ModulesModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModulesModal);\nvar _c;\n$RefreshReg$(_c, \"ModulesModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/ModulesModal.js\n"));

/***/ })

});