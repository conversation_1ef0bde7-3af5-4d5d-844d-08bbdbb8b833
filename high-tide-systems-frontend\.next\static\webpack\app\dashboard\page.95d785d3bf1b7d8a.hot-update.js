"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/calendar/components/CalendarWrapper.js":
/*!**************************************************************************!*\
  !*** ./src/app/modules/scheduler/calendar/components/CalendarWrapper.js ***!
  \**************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _fullcalendar_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @fullcalendar/react */ \"(app-pages-browser)/./node_modules/@fullcalendar/react/dist/index.js\");\n/* harmony import */ var _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @fullcalendar/daygrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/daygrid/index.js\");\n/* harmony import */ var _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @fullcalendar/timegrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/timegrid/index.js\");\n/* harmony import */ var _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @fullcalendar/interaction */ \"(app-pages-browser)/./node_modules/@fullcalendar/interaction/index.js\");\n/* harmony import */ var _fullcalendar_core_locales_pt_br__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fullcalendar/core/locales/pt-br */ \"(app-pages-browser)/./node_modules/@fullcalendar/core/locales/pt-br.js\");\n/* harmony import */ var _utils_calendarStyles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/calendarStyles */ \"(app-pages-browser)/./src/app/modules/scheduler/calendar/utils/calendarStyles.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CalendarWrapper = (param)=>{\n    let { calendarRef, isDarkMode, isLoading, appointments, businessHours, handleDateSelect, handleEventClick, handleDatesSet, renderEventContent, handleSlotClassNames, canCreateAppointment, onShowMultipleEvents, customButtons, onExport } = param;\n    _s();\n    // Limpar todos os indicadores quando o componente for desmontado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CalendarWrapper.useEffect\": ()=>{\n            // Remover a variável CSS para altura fixa dos eventos\n            document.documentElement.style.removeProperty('--fc-event-min-height');\n            // Adicionar estilo global para animação\n            const styleElement = document.createElement('style');\n            styleElement.id = 'multiple-events-global-style';\n            styleElement.textContent = \"\\n      /* Permitir que eventos sobrepostos tenham altura proporcional \\xe0 dura\\xe7\\xe3o */\\n      .fc-event.has-overlapping-events {\\n        /* N\\xe3o aplicar absolutamente nenhum estilo diferente */\\n      }\\n\\n      /* Garantir que o FullCalendar calcule a altura dos eventos com base na dura\\xe7\\xe3o */\\n      .fc-timegrid-event.has-overlapping-events {\\n        /* Remover restri\\xe7\\xf5es de altura para permitir tamanho proporcional */\\n        height: auto !important;\\n        min-height: auto !important;\\n        max-height: none !important;\\n      }\\n\\n      /* Permitir que todos os eventos tenham altura proporcional \\xe0 dura\\xe7\\xe3o */\\n      .fc-timegrid-event {\\n        height: auto !important;\\n        min-height: auto !important;\\n        max-height: none !important;\\n      }\\n\\n      /* Limitar a largura da coluna de eventos, deixando espa\\xe7o \\xe0 direita */\\n      .fc-timegrid-col-events {\\n        width: 90% !important;\\n        max-width: 90% !important;\\n        right: auto !important;\\n      }\\n\\n      /* Garantir que n\\xe3o haja espa\\xe7o entre eventos no mesmo hor\\xe1rio */\\n      .fc-timegrid-event-harness {\\n        left: 0 !important;\\n      }\\n\\n      .fc-timegrid-event-harness + .fc-timegrid-event-harness {\\n        margin-left: 0 !important;\\n        left: 0 !important;\\n      }\\n\\n      /* Adicionar apenas um pequeno badge no canto superior direito */\\n      .multiple-events-badge {\\n        position: absolute !important;\\n        top: 2px !important;\\n        right: 2px !important;\\n        width: 18px !important;\\n        height: 18px !important;\\n        background-color: #9333ea !important; /* Roxo mais escuro (violet-600) diferente do status Pendente (#8b5cf6) */\\n        color: white !important;\\n        border-radius: 50% !important;\\n        display: flex !important;\\n        align-items: center !important;\\n        justify-content: center !important;\\n        font-size: 12px !important;\\n        font-weight: bold !important;\\n        line-height: 1 !important;\\n        z-index: 1000 !important; /* Valor alto para garantir que fique acima de outros elementos */\\n        cursor: pointer !important;\\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;\\n        border: 1px solid white !important;\\n        pointer-events: auto !important; /* Garantir que o badge receba eventos de clique */\\n        margin: 0 !important; /* Remover margens */\\n        padding: 0 !important; /* Remover padding */\\n        transform: none !important; /* Remover transforma\\xe7\\xf5es */\\n      }\\n\\n      /* Garantir que o badge seja clic\\xe1vel e n\\xe3o interfira no evento principal */\\n      .multiple-events-badge:hover {\\n        /* Usar um efeito hover mais sutil que n\\xe3o afete o layout */\\n        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4) !important;\\n        filter: brightness(1.1) !important;\\n      }\\n    \";\n            document.head.appendChild(styleElement);\n            return ({\n                \"CalendarWrapper.useEffect\": ()=>{\n                    // Remover o estilo global\n                    const globalStyle = document.getElementById('multiple-events-global-style');\n                    if (globalStyle) {\n                        globalStyle.remove();\n                    }\n                    // Remover a variável CSS\n                    document.documentElement.style.removeProperty('--fc-event-min-height');\n                    // Remover badges e outros elementos\n                    const badges = document.querySelectorAll('.multiple-events-badge');\n                    badges.forEach({\n                        \"CalendarWrapper.useEffect\": (el)=>{\n                            el.remove();\n                        }\n                    }[\"CalendarWrapper.useEffect\"]);\n                    // Remover outros indicadores antigos (compatibilidade)\n                    const indicators = document.querySelectorAll('button[class*=\"multiple-events-indicator-\"]');\n                    indicators.forEach({\n                        \"CalendarWrapper.useEffect\": (el)=>{\n                            el.remove();\n                        }\n                    }[\"CalendarWrapper.useEffect\"]);\n                }\n            })[\"CalendarWrapper.useEffect\"];\n        }\n    }[\"CalendarWrapper.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-black/30\",\n        children: [\n            isDarkMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: (0,_utils_calendarStyles__WEBPACK_IMPORTED_MODULE_2__.getDarkModeStyles)()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n                lineNumber: 135,\n                columnNumber: 22\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: (0,_utils_calendarStyles__WEBPACK_IMPORTED_MODULE_2__.getEventWidthStyles)()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                children: \"\\n        .fc-exportButton-button {\\n          background-color: #f97316 !important; /* Cor laranja do m\\xf3dulo scheduler */\\n          border-color: #ea580c !important;\\n          color: white !important;\\n          font-weight: 500 !important;\\n          padding: 0.375rem 0.75rem !important;\\n          border-radius: 0.375rem !important;\\n          margin-right: 0.5rem !important;\\n        }\\n        .fc-exportButton-button:hover {\\n          background-color: #ea580c !important;\\n          border-color: #c2410c !important;\\n        }\\n        .fc-exportButton-button:focus {\\n          box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.5) !important;\\n          outline: none !important;\\n        }\\n        .dark .fc-exportButton-button {\\n          background-color: #ea580c !important;\\n          border-color: #c2410c !important;\\n        }\\n        .dark .fc-exportButton-button:hover {\\n          background-color: #c2410c !important;\\n          border-color: #9a3412 !important;\\n        }\\n      \"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative rounded-lg overflow-hidden \".concat(isLoading ? \"opacity-50\" : \"\"),\n                children: [\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-50 dark:bg-opacity-50 flex items-center justify-center z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-8 h-8 text-primary-500 dark:text-primary-400 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fullcalendar_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        ref: calendarRef,\n                        plugins: [\n                            _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                            _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                            _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                        ],\n                        initialView: \"dayGridMonth\",\n                        locale: _fullcalendar_core_locales_pt_br__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        allDaySlot: false,\n                        buttonText: {\n                            today: \"Hoje\",\n                            month: \"Mês\",\n                            week: \"Semana\",\n                            day: \"Dia\"\n                        },\n                        headerToolbar: {\n                            left: \"prev,next today\",\n                            center: \"title\",\n                            right: \"exportButton dayGridMonth,timeGridWeek,timeGridDay\"\n                        },\n                        customButtons: {\n                            exportButton: {\n                                text: 'Exportar',\n                                click: function() {\n                                    if (onExport) {\n                                        // Remover qualquer dropdown existente primeiro\n                                        const existingDropdown = document.getElementById('calendar-export-dropdown');\n                                        if (existingDropdown) {\n                                            existingDropdown.remove();\n                                        }\n                                        // Obter o botão de exportação\n                                        const button = document.querySelector('.fc-exportButton-button');\n                                        if (!button) return;\n                                        // Criar um container para o dropdown\n                                        const dropdownContainer = document.createElement('div');\n                                        dropdownContainer.id = 'calendar-export-dropdown-container';\n                                        dropdownContainer.style.position = 'absolute';\n                                        dropdownContainer.style.top = '0';\n                                        dropdownContainer.style.left = '0';\n                                        dropdownContainer.style.width = '100%';\n                                        dropdownContainer.style.height = '100%';\n                                        dropdownContainer.style.pointerEvents = 'none';\n                                        dropdownContainer.style.zIndex = '1000';\n                                        // Criar o dropdown\n                                        const dropdown = document.createElement('div');\n                                        dropdown.id = 'calendar-export-dropdown';\n                                        dropdown.style.position = 'absolute';\n                                        dropdown.style.pointerEvents = 'auto';\n                                        dropdown.style.backgroundColor = isDarkMode ? '#374151' : '#ffffff';\n                                        dropdown.style.border = isDarkMode ? '1px solid #4b5563' : '1px solid #e5e7eb';\n                                        dropdown.style.borderRadius = '0.375rem';\n                                        dropdown.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';\n                                        dropdown.style.width = '150px';\n                                        dropdown.style.padding = '0.5rem 0';\n                                        // Posicionar o dropdown - esta é a parte crítica\n                                        const buttonRect = button.getBoundingClientRect();\n                                        const headerToolbar = button.closest('.fc-header-toolbar');\n                                        if (headerToolbar) {\n                                            // Posicionar o dropdown em relação ao header toolbar\n                                            const headerRect = headerToolbar.getBoundingClientRect();\n                                            // Calcular a posição do dropdown\n                                            // Posicionar o dropdown abaixo do botão\n                                            dropdown.style.top = \"\".concat(buttonRect.bottom - headerRect.top + 5, \"px\");\n                                            // Alinhar com a esquerda do botão\n                                            dropdown.style.left = \"\".concat(buttonRect.left - headerRect.left, \"px\");\n                                            // Adicionar o dropdown ao container\n                                            dropdownContainer.appendChild(dropdown);\n                                            // Adicionar o container ao header toolbar\n                                            headerToolbar.style.position = 'relative';\n                                            headerToolbar.appendChild(dropdownContainer);\n                                        } else {\n                                            // Fallback - adicionar ao body com posição fixa\n                                            dropdown.style.position = 'fixed';\n                                            dropdown.style.top = \"\".concat(buttonRect.bottom + 5, \"px\");\n                                            dropdown.style.left = \"\".concat(buttonRect.left, \"px\");\n                                            document.body.appendChild(dropdown);\n                                        }\n                                        // Opções de exportação\n                                        const options = [\n                                            {\n                                                text: 'Imagem (PNG)',\n                                                value: 'image'\n                                            },\n                                            {\n                                                text: 'PDF',\n                                                value: 'pdf'\n                                            },\n                                            {\n                                                text: 'Excel (XLSX)',\n                                                value: 'xlsx'\n                                            }\n                                        ];\n                                        options.forEach((option)=>{\n                                            const item = document.createElement('div');\n                                            item.className = 'calendar-export-option';\n                                            item.textContent = option.text;\n                                            item.style.padding = '0.5rem 1rem';\n                                            item.style.cursor = 'pointer';\n                                            item.style.color = isDarkMode ? '#e5e7eb' : '#374151';\n                                            item.style.fontSize = '0.875rem';\n                                            item.addEventListener('mouseover', ()=>{\n                                                item.style.backgroundColor = isDarkMode ? '#4b5563' : '#f3f4f6';\n                                            });\n                                            item.addEventListener('mouseout', ()=>{\n                                                item.style.backgroundColor = 'transparent';\n                                            });\n                                            item.addEventListener('click', ()=>{\n                                                onExport(option.value);\n                                                dropdownContainer.remove();\n                                            });\n                                            dropdown.appendChild(item);\n                                        });\n                                        // Fechar dropdown ao clicar fora\n                                        const closeDropdown = (e)=>{\n                                            if (!dropdown.contains(e.target) && e.target !== button) {\n                                                dropdownContainer.remove();\n                                                document.removeEventListener('click', closeDropdown);\n                                            }\n                                        };\n                                        // Usar setTimeout para evitar que o evento de clique atual feche o dropdown\n                                        setTimeout(()=>{\n                                            document.addEventListener('click', closeDropdown);\n                                        }, 0);\n                                    }\n                                }\n                            }\n                        },\n                        views: {\n                            dayGridMonth: {\n                                // Configurações para a visualização de mês\n                                defaultTimedEventDuration: \"01:00:00\"\n                            },\n                            timeGridWeek: {\n                                slotDuration: \"00:30:00\",\n                                slotLabelFormat: {\n                                    hour: \"2-digit\",\n                                    minute: \"2-digit\",\n                                    hour12: false\n                                },\n                                slotLabelInterval: \"01:00\",\n                                snapDuration: \"01:00:00\",\n                                defaultTimedEventDuration: \"01:00:00\",\n                                nowIndicator: true,\n                                slotMinHeight: 55\n                            },\n                            timeGridDay: {\n                                // Configurações para a visualização de dia\n                                slotDuration: \"00:30:00\",\n                                slotLabelInterval: \"01:00\",\n                                snapDuration: \"01:00:00\",\n                                defaultTimedEventDuration: \"01:00:00\",\n                                nowIndicator: true,\n                                slotMinHeight: 55\n                            }\n                        },\n                        // Configurações para eventos sobrepostos\n                        eventOverlap: true,\n                        // Não agrupar eventos sobrepostos\n                        eventOrder: \"start,-duration,title\",\n                        // Permitir que eventos sobrepostos sejam exibidos lado a lado\n                        slotEventOverlap: false,\n                        // Permitir que eventos tenham altura proporcional à duração\n                        // Adicionar classes personalizadas aos eventos - abordagem simplificada\n                        eventClassNames: (arg)=>{\n                            // Adicionar classes com base no tipo de visualização\n                            if (arg.view.type === 'timeGridWeek' || arg.view.type === 'timeGridDay') {\n                                // Verificar se há eventos no mesmo horário - abordagem mais simples\n                                const date = arg.event.start;\n                                const dateStr = date.toISOString().split('T')[0];\n                                const hour = date.getHours();\n                                const minute = date.getMinutes();\n                                // Encontrar eventos no mesmo horário exato\n                                const sameTimeEvents = appointments.filter((evt)=>{\n                                    if (evt.id === arg.event.id) return false;\n                                    const evtDate = new Date(evt.start);\n                                    return evtDate.toISOString().split('T')[0] === dateStr && evtDate.getHours() === hour && evtDate.getMinutes() === minute;\n                                });\n                                // Adicionar classe especial para eventos sobrepostos\n                                if (sameTimeEvents.length > 0) {\n                                    return [\n                                        'calendar-timegrid-event',\n                                        'has-overlapping-events',\n                                        'multiple-events-indicator'\n                                    ];\n                                }\n                                return [\n                                    'calendar-timegrid-event'\n                                ];\n                            }\n                            return [];\n                        },\n                        // Adicionar indicador de múltiplos eventos apenas no último evento sobreposto\n                        // e expandir eventos que não têm outros eventos seguidos\n                        eventDidMount: (info)=>{\n                            // Verificar se estamos em visualização de semana ou dia\n                            if (info.view.type === 'timeGridWeek' || info.view.type === 'timeGridDay') {\n                                // Verificar se há eventos no mesmo horário\n                                const date = info.event.start;\n                                const dateStr = date.toISOString().split('T')[0];\n                                const hour = date.getHours();\n                                const minute = date.getMinutes();\n                                // Verificar se há eventos seguidos a este\n                                const hasFollowingEvents = appointments.some((evt)=>{\n                                    if (evt.id === info.event.id) return false;\n                                    const evtDate = new Date(evt.start);\n                                    const evtDateStr = evtDate.toISOString().split('T')[0];\n                                    // Verificar se é no mesmo dia e se começa exatamente quando este termina\n                                    return evtDateStr === dateStr && evtDate.getHours() === (info.event.end ? info.event.end.getHours() : hour + 1) && evtDate.getMinutes() === (info.event.end ? info.event.end.getMinutes() : minute);\n                                });\n                                // Se não houver eventos seguidos, expandir este evento para ocupar mais espaço\n                                if (!hasFollowingEvents && info.event.end) {\n                                    // Calcular a duração atual em minutos\n                                    const durationMinutes = (info.event.end - info.event.start) / (60 * 1000);\n                                    // Se a duração for de 1 hora, expandir para 2 horas visualmente\n                                    if (durationMinutes === 60) {\n                                        // Obter a altura de um slot de 1 hora\n                                        const slotHeight = info.view.type === 'timeGridWeek' ? 55 : 55; // Usar o mesmo valor de slotMinHeight\n                                        // Expandir para ocupar o espaço de 2 horas\n                                        info.el.style.height = \"\".concat(slotHeight * 2, \"px\");\n                                        info.el.style.maxHeight = 'none';\n                                    }\n                                }\n                                // Encontrar eventos no mesmo horário\n                                const sameTimeEvents = appointments.filter((evt)=>{\n                                    if (evt.id === info.event.id) return false;\n                                    const evtDate = new Date(evt.start);\n                                    return evtDate.toISOString().split('T')[0] === dateStr && evtDate.getHours() === hour && evtDate.getMinutes() === minute;\n                                });\n                                // Se houver eventos sobrepostos\n                                if (sameTimeEvents.length > 0) {\n                                    // Adicionar a classe sem modificar outros estilos\n                                    info.el.classList.add('has-overlapping-events');\n                                    // Não definir altura fixa para permitir tamanho proporcional à duração\n                                    // Determinar se este é o último evento sobreposto\n                                    // Primeiro, obter todos os eventos neste horário, incluindo o atual\n                                    const allEvents = [\n                                        ...sameTimeEvents,\n                                        {\n                                            id: info.event.id,\n                                            start: info.event.start,\n                                            // Extrair o timestamp do ID, se possível (assumindo que o ID contém um timestamp)\n                                            timestamp: info.event.id.match(/\\d{13}/) ? parseInt(info.event.id.match(/\\d{13}/)[0]) : 0\n                                        }\n                                    ];\n                                    // Tentar extrair timestamps dos IDs dos eventos para ordenação\n                                    allEvents.forEach((evt)=>{\n                                        if (!evt.timestamp && typeof evt.id === 'string') {\n                                            // Tentar extrair um timestamp do ID (assumindo formato que contém timestamp)\n                                            const match = evt.id.match(/\\d{13}/);\n                                            evt.timestamp = match ? parseInt(match[0]) : 0;\n                                        }\n                                    });\n                                    // Abordagem mais direta: adicionar o badge a todos os eventos e depois remover dos que não são o mais à direita\n                                    // Primeiro, vamos adicionar um identificador temporário a este evento\n                                    const currentEventId = info.event.id;\n                                    // Vamos adicionar o badge a este evento\n                                    const badge = document.createElement('div');\n                                    badge.className = 'multiple-events-badge';\n                                    badge.innerHTML = \"\".concat(sameTimeEvents.length + 1);\n                                    badge.title = \"Clique para ver todos os eventos neste horário\";\n                                    badge.dataset.eventId = currentEventId; // Armazenar o ID do evento no badge\n                                    badge.dataset.hour = \"\".concat(hour, \":\").concat(minute); // Armazenar o horário no badge\n                                    badge.dataset.date = dateStr; // Armazenar a data no badge\n                                    // Estilizar o badge\n                                    badge.style.position = \"absolute\";\n                                    badge.style.top = \"2px\";\n                                    badge.style.right = \"2px\";\n                                    badge.style.width = \"18px\";\n                                    badge.style.height = \"18px\";\n                                    badge.style.padding = \"0\";\n                                    badge.style.margin = \"0\";\n                                    badge.style.zIndex = \"1000\";\n                                    badge.style.pointerEvents = \"auto\";\n                                    badge.style.boxSizing = \"content-box\";\n                                    badge.style.overflow = \"visible\";\n                                    badge.style.backgroundColor = \"#9333ea\"; // Roxo mais escuro (violet-600)\n                                    badge.style.color = \"white\";\n                                    badge.style.borderRadius = \"50%\";\n                                    badge.style.display = \"flex\";\n                                    badge.style.alignItems = \"center\";\n                                    badge.style.justifyContent = \"center\";\n                                    badge.style.fontSize = \"12px\";\n                                    badge.style.fontWeight = \"bold\";\n                                    badge.style.lineHeight = \"1\";\n                                    badge.style.boxShadow = \"0 1px 3px rgba(0, 0, 0, 0.3)\";\n                                    badge.style.border = \"1px solid white\";\n                                    // Adicionar evento de clique ao badge\n                                    badge.addEventListener('click', (e)=>{\n                                        // Parar a propagação do evento\n                                        e.stopPropagation();\n                                        e.preventDefault();\n                                        // Encontrar todos os eventos no mesmo horário\n                                        const allModalEvents = appointments.filter((evt)=>{\n                                            const evtDate = new Date(evt.start);\n                                            return evtDate.toISOString().split('T')[0] === dateStr && evtDate.getHours() === hour && evtDate.getMinutes() === minute;\n                                        });\n                                        // Mostrar o modal com todos os eventos\n                                        if (onShowMultipleEvents) {\n                                            onShowMultipleEvents(date, allModalEvents);\n                                        }\n                                    });\n                                    // Adicionar o badge ao evento\n                                    const eventContent = info.el.querySelector('.fc-event-main');\n                                    if (eventContent) {\n                                        eventContent.style.position = 'relative';\n                                        eventContent.appendChild(badge);\n                                    } else {\n                                        info.el.appendChild(badge);\n                                    }\n                                    // Definir um timeout para verificar e remover badges duplicados\n                                    // Isso será executado após todos os eventos serem renderizados\n                                    setTimeout(()=>{\n                                        // Encontrar todos os badges para este horário específico\n                                        const allBadges = document.querySelectorAll('.multiple-events-badge[data-date=\"'.concat(dateStr, '\"][data-hour=\"').concat(hour, \":\").concat(minute, '\"]'));\n                                        if (allBadges.length > 1) {\n                                            // Encontrar o evento mais à direita\n                                            let rightmostBadge = null;\n                                            let maxRight = -1;\n                                            allBadges.forEach((badgeEl)=>{\n                                                const rect = badgeEl.getBoundingClientRect();\n                                                if (rect.right > maxRight) {\n                                                    maxRight = rect.right;\n                                                    rightmostBadge = badgeEl;\n                                                }\n                                            });\n                                            // Remover todos os badges exceto o mais à direita\n                                            allBadges.forEach((badgeEl)=>{\n                                                if (badgeEl !== rightmostBadge) {\n                                                    badgeEl.remove();\n                                                }\n                                            });\n                                        }\n                                    }, 100);\n                                // Não precisamos do bloco if (isLastEvent) pois já adicionamos o badge acima\n                                }\n                            }\n                        },\n                        slotMinTime: \"08:00:00\",\n                        slotMaxTime: \"20:00:00\",\n                        selectable: canCreateAppointment,\n                        selectMirror: true,\n                        defaultTimedEventDuration: \"01:00:00\" // Definir duração padrão de eventos para 1 hora\n                        ,\n                        forceEventDuration: true,\n                        dayMaxEvents: 3,\n                        weekends: true,\n                        hiddenDays: [\n                            0\n                        ],\n                        events: appointments,\n                        select: (selectInfo)=>{\n                            var _selectInfo_start, _selectInfo_end;\n                            console.log('[CALENDAR-SELECT] Evento de seleção original:', {\n                                start: (_selectInfo_start = selectInfo.start) === null || _selectInfo_start === void 0 ? void 0 : _selectInfo_start.toLocaleString(),\n                                end: (_selectInfo_end = selectInfo.end) === null || _selectInfo_end === void 0 ? void 0 : _selectInfo_end.toLocaleString(),\n                                temEnd: !!selectInfo.end,\n                                duracaoMinutos: selectInfo.end ? (selectInfo.end - selectInfo.start) / (60 * 1000) : 'N/A'\n                            });\n                            handleDateSelect(selectInfo);\n                        },\n                        eventClick: handleEventClick,\n                        eventContent: renderEventContent,\n                        height: \"auto\",\n                        dayHeaderClassNames: \"text-sm font-medium \".concat(isDarkMode ? 'text-gray-300' : 'text-neutral-700', \" py-3\"),\n                        slotLabelClassNames: \"text-sm \".concat(isDarkMode ? 'text-gray-300' : 'text-neutral-600'),\n                        moreLinkContent: (args)=>\"+\".concat(args.num, \" agendamentos\"),\n                        noEventsText: \"Nenhum agendamento\",\n                        moreLinkClassNames: \"text-xs font-medium \".concat(isDarkMode ? 'text-primary-400 hover:text-primary-300' : 'text-primary-600 hover:text-primary-700', \" hover:underline\"),\n                        datesSet: (dateInfo)=>{\n                            // Limpar todos os indicadores de múltiplos eventos quando a visualização mudar\n                            // Usar um seletor mais específico para garantir que pegamos todos os indicadores\n                            const indicators = document.querySelectorAll('button[class*=\"multiple-events-indicator-\"]');\n                            indicators.forEach((el)=>{\n                                el.remove();\n                            });\n                            // Chamar o handler original\n                            handleDatesSet(dateInfo);\n                        },\n                        businessHours: businessHours,\n                        businessHoursHighlight: true,\n                        businessHoursNonBusinessDaysClass: isDarkMode ? \"bg-gray-700\" : \"bg-neutral-200\",\n                        businessHoursNonBusinessHoursClass: isDarkMode ? \"bg-gray-700\" : \"bg-neutral-200\",\n                        slotLaneClassNames: handleSlotClassNames\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\calendar\\\\components\\\\CalendarWrapper.js\",\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarWrapper, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = CalendarWrapper;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CalendarWrapper);\nvar _c;\n$RefreshReg$(_c, \"CalendarWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/calendar/components/CalendarWrapper.js\n"));

/***/ })

});