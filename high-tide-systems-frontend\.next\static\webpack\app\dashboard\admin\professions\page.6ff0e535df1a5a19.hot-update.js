"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js":
/*!**********************************************************!*\
  !*** ./src/components/admin/ProfessionGroupFormModal.js ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,FileText,Info,Loader2,Search,Settings,Shield,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/permissionConfig */ \"(app-pages-browser)/./src/utils/permissionConfig.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ProfessionGroupFormModal = (param)=>{\n    let { isOpen, onClose, group = null, onSuccess } = param;\n    _s();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === \"SYSTEM_ADMIN\";\n    // Estado para controlar a tab ativa\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"info\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        companyId: \"\",\n        active: true,\n        defaultModules: [\n            \"BASIC\"\n        ],\n        defaultPermissions: []\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedModules, setExpandedModules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredPermissions, setFilteredPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Função para mudar de tab\n    const handleTabChange = (tab)=>{\n        setActiveTab(tab);\n    };\n    // Carregar empresas quando o modal é aberto (apenas para system admin)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionGroupFormModal.useEffect\": ()=>{\n            if (isOpen && isSystemAdmin) {\n                loadCompanies();\n            }\n        }\n    }[\"ProfessionGroupFormModal.useEffect\"], [\n        isOpen,\n        isSystemAdmin\n    ]);\n    // Carregar dados do grupo quando selecionado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionGroupFormModal.useEffect\": ()=>{\n            if (group) {\n                setFormData({\n                    name: group.name || \"\",\n                    description: group.description || \"\",\n                    companyId: group.companyId || \"\",\n                    active: group.active !== undefined ? group.active : true,\n                    defaultModules: group.defaultModules || [\n                        \"BASIC\"\n                    ],\n                    defaultPermissions: group.defaultPermissions || []\n                });\n            } else {\n                setFormData({\n                    name: \"\",\n                    description: \"\",\n                    companyId: (user === null || user === void 0 ? void 0 : user.companyId) || \"\",\n                    active: true,\n                    defaultModules: [\n                        \"BASIC\"\n                    ],\n                    defaultPermissions: []\n                });\n            }\n        }\n    }[\"ProfessionGroupFormModal.useEffect\"], [\n        group,\n        user\n    ]);\n    // Inicializar permissões filtradas quando o modal é aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionGroupFormModal.useEffect\": ()=>{\n            if (isOpen) {\n                setFilteredPermissions((0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_6__.getAllPermissions)());\n            }\n        }\n    }[\"ProfessionGroupFormModal.useEffect\"], [\n        isOpen\n    ]);\n    // Filtrar permissões com base no termo de busca\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionGroupFormModal.useEffect\": ()=>{\n            if (!searchTerm.trim()) {\n                setFilteredPermissions((0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_6__.getAllPermissions)());\n                return;\n            }\n            const searchTermLower = searchTerm.toLowerCase();\n            const filtered = (0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_6__.getAllPermissions)().filter({\n                \"ProfessionGroupFormModal.useEffect.filtered\": (permission)=>permission.name.toLowerCase().includes(searchTermLower) || permission.description.toLowerCase().includes(searchTermLower) || permission.id.toLowerCase().includes(searchTermLower) || _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_6__.PERMISSIONS_CONFIG[permission.moduleId].name.toLowerCase().includes(searchTermLower)\n            }[\"ProfessionGroupFormModal.useEffect.filtered\"]);\n            setFilteredPermissions(filtered);\n            // Expandir módulos que têm permissões correspondentes\n            const modulesToExpand = {};\n            filtered.forEach({\n                \"ProfessionGroupFormModal.useEffect\": (permission)=>{\n                    modulesToExpand[permission.moduleId] = true;\n                }\n            }[\"ProfessionGroupFormModal.useEffect\"]);\n            setExpandedModules({\n                \"ProfessionGroupFormModal.useEffect\": (prev)=>({\n                        ...prev,\n                        ...modulesToExpand\n                    })\n            }[\"ProfessionGroupFormModal.useEffect\"]);\n        }\n    }[\"ProfessionGroupFormModal.useEffect\"], [\n        searchTerm\n    ]);\n    // Função para carregar empresas\n    const loadCompanies = async ()=>{\n        setIsLoadingCompanies(true);\n        try {\n            const companies = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_3__.companyService.getCompaniesForSelect();\n            setCompanies(companies || []);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    const handleChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData({\n            ...formData,\n            [name]: type === \"checkbox\" ? checked : value\n        });\n    };\n    // Função para alternar um módulo\n    const handleToggleModule = (moduleId)=>{\n        if (moduleId === \"BASIC\") return; // BASIC é obrigatório\n        setFormData((prev)=>{\n            const defaultModules = [\n                ...prev.defaultModules\n            ];\n            if (defaultModules.includes(moduleId)) {\n                return {\n                    ...prev,\n                    defaultModules: defaultModules.filter((m)=>m !== moduleId)\n                };\n            } else {\n                return {\n                    ...prev,\n                    defaultModules: [\n                        ...defaultModules,\n                        moduleId\n                    ]\n                };\n            }\n        });\n    };\n    // Função para alternar uma permissão\n    const handleTogglePermission = (permissionId)=>{\n        setFormData((prev)=>{\n            const defaultPermissions = [\n                ...prev.defaultPermissions\n            ];\n            if (defaultPermissions.includes(permissionId)) {\n                return {\n                    ...prev,\n                    defaultPermissions: defaultPermissions.filter((p)=>p !== permissionId)\n                };\n            } else {\n                return {\n                    ...prev,\n                    defaultPermissions: [\n                        ...defaultPermissions,\n                        permissionId\n                    ]\n                };\n            }\n        });\n    };\n    // Função para alternar a expansão de um módulo\n    const toggleModuleExpansion = (moduleId)=>{\n        setExpandedModules((prev)=>({\n                ...prev,\n                [moduleId]: !prev[moduleId]\n            }));\n    };\n    // Alternar todas as permissões de um módulo\n    const toggleModulePermissions = (moduleId)=>{\n        const moduleConfig = _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_6__.PERMISSIONS_CONFIG[moduleId];\n        if (!moduleConfig) return;\n        const permissions = moduleConfig.permissions;\n        if (!permissions || permissions.length === 0) return;\n        // Verificar se todas as permissões do módulo estão selecionadas\n        const allSelected = permissions.every((p)=>formData.defaultPermissions.includes(p.id));\n        // Se todas estiverem selecionadas, remover todas; caso contrário, adicionar todas\n        if (allSelected) {\n            // Remover todas as permissões deste módulo\n            const updatedPermissions = formData.defaultPermissions.filter((id)=>!permissions.some((p)=>p.id === id));\n            setFormData((prev)=>({\n                    ...prev,\n                    defaultPermissions: updatedPermissions\n                }));\n        } else {\n            // Adicionar todas as permissões deste módulo que ainda não estão selecionadas\n            const permissionsToAdd = permissions.filter((p)=>!formData.defaultPermissions.includes(p.id)).map((p)=>p.id);\n            setFormData((prev)=>({\n                    ...prev,\n                    defaultPermissions: [\n                        ...prev.defaultPermissions,\n                        ...permissionsToAdd\n                    ]\n                }));\n        }\n    };\n    const handleSubmit = async ()=>{\n        setIsSubmitting(true);\n        try {\n            // Validar dados\n            if (!formData.name.trim()) {\n                toast_error(\"O nome do grupo é obrigatório\");\n                setIsSubmitting(false);\n                return;\n            }\n            // Garantir que BASIC esteja sempre incluído nos módulos padrão\n            if (!formData.defaultModules.includes(\"BASIC\")) {\n                formData.defaultModules.push(\"BASIC\");\n            }\n            // Preparar dados para envio\n            const dataToSend = {\n                name: formData.name.trim(),\n                description: formData.description.trim() || undefined,\n                active: formData.active,\n                defaultModules: formData.defaultModules,\n                defaultPermissions: formData.defaultPermissions\n            };\n            // Adicionar companyId apenas se for system admin\n            if (isSystemAdmin) {\n                dataToSend.companyId = formData.companyId || null;\n            }\n            if (group) {\n                // Modo de edição\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_2__.professionsService.updateProfessionGroup(group.id, dataToSend);\n                toast_success(\"Grupo atualizado com sucesso\");\n            } else {\n                // Modo de criação\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_2__.professionsService.createProfessionGroup(dataToSend);\n                toast_success(\"Grupo criado com sucesso\");\n            }\n            if (onSuccess) onSuccess();\n            onClose(); // Fechar o modal após salvar com sucesso\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao salvar grupo:\", error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao salvar grupo\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Classes CSS comuns\n    const inputClasses = \"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\";\n    const iconContainerClasses = \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\";\n    const labelClasses = \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\";\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[11050]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-neutral-800 dark:text-white\",\n                                children: group ? \"Editar Grupo de Profissões\" : \"Novo Grupo de Profissões\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-neutral-200 dark:border-gray-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange(\"info\"),\n                                    className: \"flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors \".concat(activeTab === \"info\" ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\" : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Informa\\xe7\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange(\"modules\"),\n                                    className: \"flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors \".concat(activeTab === \"modules\" ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\" : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"M\\xf3dulos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleTabChange(\"permissions\"),\n                                    className: \"flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors \".concat(activeTab === \"permissions\" ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\" : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Permiss\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto p-6\",\n                        children: [\n                            activeTab === \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\",\n                                                children: (group === null || group === void 0 ? void 0 : group.name) || \"Novo Grupo de Profissões\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-neutral-600 dark:text-gray-400 mb-4\",\n                                                children: \"Preencha as informa\\xe7\\xf5es b\\xe1sicas do grupo de profiss\\xf5es:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 333,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: labelClasses,\n                                                        htmlFor: \"name\",\n                                                        children: [\n                                                            \"Nome \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 26\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: iconContainerClasses,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                id: \"name\",\n                                                                name: \"name\",\n                                                                value: formData.name,\n                                                                onChange: handleChange,\n                                                                className: inputClasses,\n                                                                required: true,\n                                                                placeholder: \"Nome do grupo de profiss\\xf5es\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: labelClasses,\n                                                        htmlFor: \"description\",\n                                                        children: \"Descri\\xe7\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: iconContainerClasses,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"description\",\n                                                                name: \"description\",\n                                                                value: formData.description,\n                                                                onChange: handleChange,\n                                                                rows: \"3\",\n                                                                className: \"\".concat(inputClasses, \" resize-none\"),\n                                                                placeholder: \"Descri\\xe7\\xe3o detalhada do grupo de profiss\\xf5es\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: labelClasses,\n                                                        htmlFor: \"companyId\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: iconContainerClasses,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"companyId\",\n                                                                name: \"companyId\",\n                                                                value: formData.companyId,\n                                                                onChange: handleChange,\n                                                                className: inputClasses,\n                                                                disabled: isLoadingCompanies,\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"\",\n                                                                        children: \"Selecione uma empresa\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: company.id,\n                                                                            children: company.name\n                                                                        }, company.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 27\n                                                                        }, undefined))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    isLoadingCompanies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1 text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                size: 12,\n                                                                className: \"animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Carregando empresas...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 388,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            group && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"active\",\n                                                        name: \"active\",\n                                                        checked: formData.active,\n                                                        onChange: handleChange,\n                                                        className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"active\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Grupo ativo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"modules\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\",\n                                                children: (group === null || group === void 0 ? void 0 : group.name) || \"Novo Grupo de Profissões\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-neutral-600 dark:text-gray-400 mb-4\",\n                                                children: \"Selecione os m\\xf3dulos padr\\xe3o que ser\\xe3o atribu\\xeddos aos usu\\xe1rios deste grupo de profiss\\xf5es:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg border \".concat(formData.defaultModules.includes(\"ADMIN\") ? \"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-primary-300 dark:hover:border-primary-700\"),\n                                                onClick: ()=>handleToggleModule(\"ADMIN\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: formData.defaultModules.includes(\"ADMIN\"),\n                                                                onChange: ()=>{},\n                                                                disabled: isSubmitting,\n                                                                className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-5 w-5 \".concat(formData.defaultModules.includes(\"ADMIN\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-medium text-neutral-800 dark:text-white\",\n                                                                            children: \"Administra\\xe7\\xe3o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                                                    children: \"Gerenciamento de usu\\xe1rios, empresas, configura\\xe7\\xf5es do sistema\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg border \".concat(formData.defaultModules.includes(\"RH\") ? \"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-primary-300 dark:hover:border-primary-700\"),\n                                                onClick: ()=>handleToggleModule(\"RH\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: formData.defaultModules.includes(\"RH\"),\n                                                                onChange: ()=>{},\n                                                                disabled: isSubmitting,\n                                                                className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-5 w-5 \".concat(formData.defaultModules.includes(\"RH\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-medium text-neutral-800 dark:text-white\",\n                                                                            children: \"Recursos Humanos\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                                                    children: \"Gerenciamento de funcion\\xe1rios, folha de pagamento, benef\\xedcios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg border \".concat(formData.defaultModules.includes(\"FINANCIAL\") ? \"bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400 border-amber-200 dark:border-amber-800/50\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-primary-300 dark:hover:border-primary-700\"),\n                                                onClick: ()=>handleToggleModule(\"FINANCIAL\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: formData.defaultModules.includes(\"FINANCIAL\"),\n                                                                onChange: ()=>{},\n                                                                disabled: isSubmitting,\n                                                                className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 \".concat(formData.defaultModules.includes(\"FINANCIAL\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-medium text-neutral-800 dark:text-white\",\n                                                                            children: \"Financeiro\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                                                    children: \"Controle de faturas, pagamentos, despesas e relat\\xf3rios financeiros\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg border \".concat(formData.defaultModules.includes(\"SCHEDULING\") ? \"bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-primary-300 dark:hover:border-primary-700\"),\n                                                onClick: ()=>handleToggleModule(\"SCHEDULING\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: formData.defaultModules.includes(\"SCHEDULING\"),\n                                                                onChange: ()=>{},\n                                                                disabled: isSubmitting,\n                                                                className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-5 w-5 \".concat(formData.defaultModules.includes(\"SCHEDULING\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-medium text-neutral-800 dark:text-white\",\n                                                                            children: \"Agendamento\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 560,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                                                    children: \"Gerenciamento de compromissos, reuni\\xf5es e aloca\\xe7\\xe3o de recursos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 rounded-lg border bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600 opacity-70\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: true,\n                                                                onChange: ()=>{},\n                                                                disabled: true,\n                                                                className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 574,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"font-medium text-neutral-800 dark:text-white\",\n                                                                            children: \"B\\xe1sico\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                                                    children: \"Acesso b\\xe1sico ao sistema, visualiza\\xe7\\xe3o limitada\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            size: 12\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"M\\xf3dulo obrigat\\xf3rio para todos os usu\\xe1rios\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 570,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 456,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"permissions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-medium text-neutral-800 dark:text-gray-200 mb-1\",\n                                                children: (group === null || group === void 0 ? void 0 : group.name) || \"Novo Grupo de Profissões\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-neutral-600 dark:text-gray-400 mb-4\",\n                                                children: \"Configure as permiss\\xf5es padr\\xe3o que ser\\xe3o atribu\\xeddas aos usu\\xe1rios deste grupo de profiss\\xf5es:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-amber-50 border border-amber-200 p-4 rounded-lg flex items-start gap-3 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 mt-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-amber-500 dark:text-amber-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"font-medium text-amber-800 dark:text-amber-300\",\n                                                                children: \"Importante\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-amber-700 dark:text-amber-400\",\n                                                                children: \"As permiss\\xf5es s\\xf3 ser\\xe3o aplicadas se o usu\\xe1rio tamb\\xe9m tiver acesso ao m\\xf3dulo correspondente. Certifique-se de que o usu\\xe1rio tenha os m\\xf3dulos necess\\xe1rios atribu\\xeddos.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 606,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 632,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Buscar permiss\\xf5es...\",\n                                                    className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 635,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                            lineNumber: 631,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg mb-6 dark:border dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                                children: \"M\\xf3dulos Padr\\xe3o\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    formData.defaultModules.map((moduleId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-1.5 bg-white dark:bg-gray-700 border dark:border-gray-600 rounded-full flex items-center gap-2\",\n                                                            children: [\n                                                                moduleId === \"ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 48\n                                                                }, undefined),\n                                                                moduleId === \"RH\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 45\n                                                                }, undefined),\n                                                                moduleId === \"FINANCIAL\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 52\n                                                                }, undefined),\n                                                                moduleId === \"SCHEDULING\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 659,\n                                                                    columnNumber: 53\n                                                                }, undefined),\n                                                                moduleId === \"BASIC\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-5 w-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 48\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm dark:text-gray-300\",\n                                                                    children: [\n                                                                        moduleId === \"ADMIN\" && \"Administração\",\n                                                                        moduleId === \"RH\" && \"Recursos Humanos\",\n                                                                        moduleId === \"FINANCIAL\" && \"Financeiro\",\n                                                                        moduleId === \"SCHEDULING\" && \"Agendamento\",\n                                                                        moduleId === \"BASIC\" && \"Básico\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, moduleId, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, undefined)),\n                                                    (!formData.defaultModules || formData.defaultModules.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                                        children: \"Nenhum m\\xf3dulo atribu\\xeddo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 650,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            Object.entries(_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_6__.PERMISSIONS_CONFIG).map((param)=>{\n                                                let [moduleId, moduleConfig] = param;\n                                                // Só mostrar módulos que o grupo tem acesso\n                                                if (!formData.defaultModules.includes(moduleId)) return null;\n                                                // Filtrar permissões deste módulo\n                                                const modulePermissions = filteredPermissions.filter((p)=>p.moduleId === moduleId);\n                                                if (modulePermissions.length === 0) return null;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-6 border rounded-lg overflow-hidden dark:border-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer\",\n                                                            onClick: ()=>toggleModuleExpansion(moduleId),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 rounded-full bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400\",\n                                                                            children: [\n                                                                                moduleId === \"ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 701,\n                                                                                    columnNumber: 54\n                                                                                }, undefined),\n                                                                                moduleId === \"RH\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 702,\n                                                                                    columnNumber: 51\n                                                                                }, undefined),\n                                                                                moduleId === \"FINANCIAL\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 703,\n                                                                                    columnNumber: 58\n                                                                                }, undefined),\n                                                                                moduleId === \"SCHEDULING\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 704,\n                                                                                    columnNumber: 59\n                                                                                }, undefined),\n                                                                                moduleId === \"BASIC\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-5 w-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 705,\n                                                                                    columnNumber: 54\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 700,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium text-neutral-800 dark:text-gray-200\",\n                                                                                    children: moduleConfig.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 708,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                                                                    children: [\n                                                                                        moduleId === \"ADMIN\" && \"Permissões para gerenciamento administrativo\",\n                                                                                        moduleId === \"RH\" && \"Permissões para recursos humanos\",\n                                                                                        moduleId === \"FINANCIAL\" && \"Permissões para gestão financeira\",\n                                                                                        moduleId === \"SCHEDULING\" && \"Permissões para agendamento\",\n                                                                                        moduleId === \"BASIC\" && \"Permissões básicas do sistema\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                toggleModulePermissions(moduleId);\n                                                                            },\n                                                                            className: \"px-3 py-1 rounded text-sm font-medium bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700\",\n                                                                            children: \"Selecionar todas\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 719,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        expandedModules[moduleId] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-neutral-500 dark:text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 730,\n                                                                            columnNumber: 29\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-neutral-500 dark:text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        expandedModules[moduleId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 divide-y dark:divide-gray-700 dark:bg-gray-850\",\n                                                            children: modulePermissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"py-3 first:pt-0 last:pb-0\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-shrink-0 mt-0.5\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: permission.id,\n                                                                                    checked: formData.defaultPermissions.includes(permission.id),\n                                                                                    onChange: ()=>handleTogglePermission(permission.id),\n                                                                                    className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                    lineNumber: 744,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                lineNumber: 743,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        htmlFor: permission.id,\n                                                                                        className: \"block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer\",\n                                                                                        children: permission.name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                        lineNumber: 754,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"mt-1 text-sm text-neutral-600 dark:text-gray-400\",\n                                                                                        children: permission.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                        lineNumber: 760,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"mt-1 text-xs text-neutral-500 dark:text-gray-500\",\n                                                                                        children: [\n                                                                                            \"ID: \",\n                                                                                            permission.id\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                        lineNumber: 763,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                                lineNumber: 753,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                        lineNumber: 742,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, permission.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 29\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, moduleId, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 693,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            }),\n                                            filteredPermissions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-neutral-500 dark:text-gray-400\",\n                                                    children: \"Nenhuma permiss\\xe3o encontrada.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                lineNumber: 778,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                lineNumber: 605,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-between items-center bg-white dark:bg-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: activeTab !== \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>{\n                                        // Definir a aba anterior com base na aba atual\n                                        const prevTab = {\n                                            modules: \"info\",\n                                            permissions: \"modules\"\n                                        }[activeTab];\n                                        setActiveTab(prevTab);\n                                    },\n                                    className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                    disabled: isSubmitting,\n                                    children: \"Voltar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                    lineNumber: 793,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                lineNumber: 791,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                        disabled: isSubmitting,\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 811,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleSubmit,\n                                        className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 828,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                activeTab === \"info\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 44\n                                                }, undefined),\n                                                activeTab === \"modules\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 833,\n                                                    columnNumber: 47\n                                                }, undefined),\n                                                activeTab === \"permissions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_FileText_Info_Loader2_Search_Settings_Shield_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 51\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: !group ? activeTab === \"permissions\" ? \"Criar Grupo\" : \"Continuar\" : \"Salvar \".concat(activeTab === \"info\" ? \"Grupo\" : activeTab === \"modules\" ? \"Módulos\" : \"Permissões\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                                    lineNumber: 835,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                        lineNumber: 819,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                                lineNumber: 810,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                        lineNumber: 790,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupFormModal.js\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionGroupFormModal, \"aXtLgf0HgpMlQ/KHGRSBhzdWRrg=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = ProfessionGroupFormModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionGroupFormModal);\nvar _c;\n$RefreshReg$(_c, \"ProfessionGroupFormModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js\n"));

/***/ })

});