// Script para adicionar módulos à assinatura existente
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function addTestModules() {
  try {
    // Buscar a assinatura existente
    const subscription = await prisma.subscription.findFirst({
      where: {
        status: 'ACTIVE'
      }
    });

    if (!subscription) {
      console.log('Nenhuma assinatura ativa encontrada.');
      return;
    }

    console.log('Usando assinatura:', subscription.id);

    // Verificar módulos existentes
    const existingModules = await prisma.subscriptionModule.findMany({
      where: {
        subscriptionId: subscription.id
      }
    });

    console.log('Módulos existentes:', existingModules.map(m => m.moduleType));

    // Criar módulos da assinatura
    const modules = [
      { moduleType: 'BASIC', pricePerMonth: 0 },
      { moduleType: 'ADMIN', pricePerMonth: 0 },
      { moduleType: 'SCHEDULING', pricePerMonth: 0 },
      { moduleType: 'RH', pricePerMonth: 49.90 },
      { moduleType: 'FINANCIAL', pricePerMonth: 29.90 }
    ];

    for (const moduleData of modules) {
      // Verificar se o módulo já existe
      const existingModule = existingModules.find(m => m.moduleType === moduleData.moduleType);
      
      if (existingModule) {
        console.log(`Módulo ${moduleData.moduleType} já existe, pulando...`);
        continue;
      }

      const createdModule = await prisma.subscriptionModule.create({
        data: {
          subscriptionId: subscription.id,
          moduleType: moduleData.moduleType,
          pricePerMonth: moduleData.pricePerMonth,
          active: true
        }
      });

      console.log(`Módulo ${moduleData.moduleType} criado:`, createdModule.id);
    }

    console.log('Módulos da assinatura criados com sucesso!');

  } catch (error) {
    console.error('Erro ao criar módulos:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestModules();
