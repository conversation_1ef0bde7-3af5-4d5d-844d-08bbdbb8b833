// Test the exact request that failed in the frontend
async function testFailedRequest() {
    try {
        console.log('🔍 Testing the exact failed request...');

        const headers = {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjcwNjE1Mzc0LThkMjMtNGE0NC1iMmM5LWNhN2Y2MzNlYzAxZSIsImlhdCI6MTc0OTk5Nzc2MSwiaXNDbGllbnQiOmZhbHNlLCJtb2R1bGVzIjpbIkJBU0lDIiwiQURNSU4iLCJSSCIsIkZJTkFOQ0lBTCIsIlNDSEVEVUxJTkciXSwicm9sZSI6IlNZU1RFTV9BRE1JTiIsImV4cCI6MTc1MDA4NDE2MX0.9ZnELdYO8_AJbu9BYV8yh5ne6LhE1pWzh7ty0S3yp2c',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        };
        
        // Test the exact URL that failed
        console.log('📋 Testing exact failed URL...');
        const failedUrl = 'http://localhost:5000/adminDashboard/plans?companyId=9c4195cf-fe76-4455-b515-44b07224706e&_t=1750000831144&_cache_bust=gxisel';

        console.log('Making request to:', failedUrl);
        const response1 = await fetch(failedUrl, { headers });

        console.log('Response status:', response1.status);
        console.log('Response headers:', Object.fromEntries(response1.headers.entries()));

        if (!response1.ok) {
            console.log('❌ Response not OK:', response1.status, response1.statusText);
            const errorText = await response1.text();
            console.log('Error body:', errorText);
            return;
        }

        const data1 = await response1.json();
        console.log('✅ Response received successfully');
        console.log('Current modules:', data1.modules?.map(m => m.moduleType) || 'No modules found');
        
        // Step 2: Try to add FINANCIAL module
        console.log('\n🔄 Step 2: Adding FINANCIAL module...');
        const addResponse = await fetch('http://localhost:5000/subscription/module/add', {
            method: 'POST',
            headers: {
                ...headers,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                moduleType: 'FINANCIAL',
                companyId: '9c4195cf-fe76-4455-b515-44b07224706e'
            })
        });
        
        const addResult = await addResponse.json();
        console.log('Add result:', addResult);
        
        // Step 3: Get modules again (should show FINANCIAL if cache was invalidated)
        console.log('\n📋 Step 3: Getting modules after addition...');
        const response2 = await fetch('http://localhost:5000/adminDashboard/plans?companyId=9c4195cf-fe76-4455-b515-44b07224706e', {
            headers
        });
        const data2 = await response2.json();
        console.log('Modules after addition:', data2.modules.map(m => m.moduleType));
        
        // Check if FINANCIAL is now in the list
        const hasFinancial = data2.modules.some(m => m.moduleType === 'FINANCIAL');
        console.log(`✅ FINANCIAL module ${hasFinancial ? 'IS' : 'IS NOT'} in the list`);
        
        if (hasFinancial) {
            console.log('🎉 Cache invalidation for ADD is working correctly!');

            // Step 4: Now test removal
            console.log('\n🗑️ Step 4: Removing FINANCIAL module...');
            const removeResponse = await fetch('http://localhost:5000/subscription/module/remove', {
                method: 'POST',
                headers: {
                    ...headers,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    moduleType: 'FINANCIAL',
                    companyId: '9c4195cf-fe76-4455-b515-44b07224706e'
                })
            });

            const removeResult = await removeResponse.json();
            console.log('Remove result:', removeResult);

            // Step 5: Get modules again (should NOT show FINANCIAL if cache was invalidated)
            console.log('\n📋 Step 5: Getting modules after removal...');
            const response3 = await fetch('http://localhost:5000/adminDashboard/plans?companyId=9c4195cf-fe76-4455-b515-44b07224706e', {
                headers
            });
            const data3 = await response3.json();
            console.log('Modules after removal:', data3.modules.map(m => m.moduleType));

            // Check if FINANCIAL is no longer in the list
            const stillHasFinancial = data3.modules.some(m => m.moduleType === 'FINANCIAL');
            console.log(`✅ FINANCIAL module ${stillHasFinancial ? 'IS STILL' : 'IS NO LONGER'} in the list`);

            if (!stillHasFinancial) {
                console.log('🎉 Cache invalidation for REMOVE is also working correctly!');
            } else {
                console.log('❌ Cache invalidation for REMOVE may not be working properly');
            }
        } else {
            console.log('❌ Cache invalidation may not be working properly');
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testFailedRequest();
