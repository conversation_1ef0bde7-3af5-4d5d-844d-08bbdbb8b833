// Script para criar dados de teste de assinatura
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createTestSubscription() {
  try {
    // Buscar a primeira empresa disponível
    const company = await prisma.company.findFirst({
      where: {
        active: true
      }
    });

    if (!company) {
      console.log('Nenhuma empresa encontrada. Criando empresa de teste...');
      
      // Criar empresa de teste
      const newCompany = await prisma.company.create({
        data: {
          name: 'Empresa Teste',
          email: '<EMAIL>',
          phone: '(11) 99999-9999',
          active: true
        }
      });
      
      console.log('Empresa criada:', newCompany);
      company = newCompany;
    }

    console.log('Usando empresa:', company.name);

    // Verificar se já existe uma assinatura para esta empresa
    const existingSubscription = await prisma.subscription.findFirst({
      where: {
        companyId: company.id
      }
    });

    if (existingSubscription) {
      console.log('Assinatura já existe para esta empresa:', existingSubscription);
      return;
    }

    // Criar assinatura de teste
    const subscription = await prisma.subscription.create({
      data: {
        companyId: company.id,
        billingCycle: 'MONTHLY',
        status: 'ACTIVE',
        pricePerMonth: 199.90,
        userLimit: 50,
        startDate: new Date(),
        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dias
      }
    });

    console.log('Assinatura criada:', subscription);

    // Criar módulos da assinatura
    const modules = [
      { moduleType: 'BASIC', pricePerMonth: 0 },
      { moduleType: 'ADMIN', pricePerMonth: 0 },
      { moduleType: 'SCHEDULING', pricePerMonth: 0 },
      { moduleType: 'RH', pricePerMonth: 49.90 },
      { moduleType: 'FINANCIAL', pricePerMonth: 29.90 }
    ];

    for (const moduleData of modules) {
      await prisma.subscriptionModule.create({
        data: {
          subscriptionId: subscription.id,
          moduleType: moduleData.moduleType,
          pricePerMonth: moduleData.pricePerMonth,
          active: true
        }
      });
    }

    console.log('Módulos da assinatura criados');
    console.log('Dados de teste criados com sucesso!');

  } catch (error) {
    console.error('Erro ao criar dados de teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestSubscription();
