"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/Sidebar/index.js":
/*!***************************************************!*\
  !*** ./src/components/dashboard/Sidebar/index.js ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ChevronDown,ChevronLeft,ChevronRight,Construction,Hammer,HardHat,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/dashboard/components */ \"(app-pages-browser)/./src/app/dashboard/components.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var _hooks_useConstructionMessage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useConstructionMessage */ \"(app-pages-browser)/./src/hooks/useConstructionMessage.js\");\n/* harmony import */ var _components_construction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/construction */ \"(app-pages-browser)/./src/components/construction/index.js\");\n/* harmony import */ var _utils_constructionUtils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/constructionUtils */ \"(app-pages-browser)/./src/utils/constructionUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ModuleTitle = (param)=>{\n    let { moduleId, title, icon: Icon } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\\n        relative overflow-hidden rounded-xl p-4 mb-4\\n        bg-gradient-to-r from-white dark:from-gray-800 to-module-\".concat(moduleId, \"-bg/30 dark:to-module-\").concat(moduleId, \"-bg-dark/30\\n        border-l-4 border-module-\").concat(moduleId, \"-border dark:border-module-\").concat(moduleId, \"-border-dark\\n        shadow-sm dark:shadow-md dark:shadow-black/20\\n      \"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            w-12 h-12 rounded-lg mr-3 flex items-center justify-center\\n            bg-module-\".concat(moduleId, \"-bg/70 dark:bg-module-\").concat(moduleId, \"-bg-dark/70\\n            text-module-\").concat(moduleId, \"-icon dark:text-module-\").concat(moduleId, \"-icon-dark\\n          \"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            size: 26\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold\",\n                                    children: \"M\\xf3dulo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800 dark:text-gray-100\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTitle;\n// Mapeamento de submenus para permissões\nconst submenuPermissionsMap = {\n    // Admin\n    'admin.introduction': 'admin.dashboard.view',\n    'admin.dashboard': 'admin.dashboard.view',\n    'admin.users': 'admin.users.view',\n    'admin.professions': [\n        'admin.professions.view',\n        'admin.profession-groups.view'\n    ],\n    'admin.plans': 'admin.dashboard.view',\n    'admin.logs': 'admin.logs.view',\n    'admin.settings': 'admin.config.edit',\n    'admin.backup': 'admin.config.edit',\n    // ABA+\n    'abaplus.anamnese': 'abaplus.anamnese.view',\n    'abaplus.evolucoes-diarias': 'abaplus.evolucoes-diarias.view',\n    'abaplus.sessao': 'abaplus.sessao.view',\n    // Financeiro\n    'financial.invoices': 'financial.invoices.view',\n    'financial.payments': 'financial.payments.view',\n    'financial.expenses': 'financial.expenses.view',\n    'financial.reports': 'financial.reports.view',\n    'financial.cashflow': 'financial.reports.view',\n    // RH\n    'hr.employees': 'rh.employees.view',\n    'hr.payroll': 'rh.payroll.view',\n    'hr.documents': 'rh.employees.view',\n    'hr.departments': 'rh.employees.view',\n    'hr.attendance': 'rh.attendance.view',\n    'hr.benefits': 'rh.benefits.view',\n    'hr.training': 'rh.employees.view',\n    // Pessoas\n    'people.clients': 'people.clients.view',\n    'people.persons': 'people.persons.view',\n    'people.insurances': 'people.insurances.view',\n    'people.insurance-limits': 'people.insurance-limits.view',\n    // Agendamento\n    'scheduler.calendar': 'scheduling.calendar.view',\n    'scheduler.working-hours': 'scheduling.working-hours.view',\n    'scheduler.service-types': 'scheduling.service-types.view',\n    'scheduler.locations': 'scheduling.locations.view',\n    'scheduler.occupancy': 'scheduling.occupancy.view',\n    'scheduler.appointments-report': 'scheduling.appointments-report.view',\n    'scheduler.appointments-dashboard': 'scheduling.appointments-dashboard.view'\n};\nconst Sidebar = (param)=>{\n    let { activeModule, activeModuleTitle, isSubmenuActive, handleModuleSubmenuClick, handleBackToModules, isSidebarOpen } = param;\n    var _moduleSubmenus_activeModule;\n    _s();\n    const { can, hasModule, isAdmin } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Inicializar o estado de grupos expandidos a partir do localStorage\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"Sidebar.useState\": ()=>{\n            // Verificar se estamos no cliente (browser) antes de acessar localStorage\n            if (true) {\n                const savedState = localStorage.getItem('sidebarExpandedGroups');\n                return savedState ? JSON.parse(savedState) : {};\n            }\n            return {};\n        }\n    }[\"Sidebar.useState\"]);\n    // Encontrar o objeto do módulo ativo para acessar seu ícone\n    const activeModuleObject = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.modules.find((m)=>m.id === activeModule);\n    const ModuleIcon = activeModuleObject === null || activeModuleObject === void 0 ? void 0 : activeModuleObject.icon;\n    // Função para verificar se o usuário tem permissão para ver um submenu\n    const hasPermissionForSubmenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenu]\": (moduleId, submenuId)=>{\n            // Ignorar verificação para grupos, pois a permissão é verificada para cada item\n            if (submenuId === 'cadastro' || submenuId === 'configuracoes' || submenuId === 'gestao' || submenuId === 'convenios' || submenuId === 'financeiro' || submenuId === 'relatorios') {\n                return true;\n            }\n            const permissionKey = \"\".concat(moduleId, \".\").concat(submenuId);\n            const requiredPermission = submenuPermissionsMap[permissionKey];\n            // Se não há mapeamento de permissão, permitir acesso\n            if (!requiredPermission) return true;\n            // Administradores têm acesso a tudo\n            if (isAdmin()) return true;\n            // Verificar permissão específica\n            if (Array.isArray(requiredPermission)) {\n                // Se for um array de permissões, verificar se o usuário tem pelo menos uma delas\n                return requiredPermission.some({\n                    \"Sidebar.useCallback[hasPermissionForSubmenu]\": (perm)=>can(perm)\n                }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"]);\n            } else {\n                // Se for uma única permissão, verificar normalmente\n                return can(requiredPermission);\n            }\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenu]\"], [\n        can,\n        isAdmin\n    ]);\n    // Função para alternar a expansão de um grupo\n    const toggleGroup = (groupId)=>{\n        setExpandedGroups((prev)=>({\n                ...prev,\n                [groupId]: !prev[groupId]\n            }));\n    };\n    // Verificar se algum item dentro de um grupo está ativo\n    const isGroupActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[isGroupActive]\": (moduleId, groupItems)=>{\n            return groupItems.some({\n                \"Sidebar.useCallback[isGroupActive]\": (item)=>isSubmenuActive(moduleId, item.id)\n            }[\"Sidebar.useCallback[isGroupActive]\"]);\n        }\n    }[\"Sidebar.useCallback[isGroupActive]\"], [\n        isSubmenuActive\n    ]);\n    // Expandir automaticamente grupos que contêm o item ativo\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            if (activeModule && _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) {\n                const newExpandedGroups = {\n                    ...expandedGroups\n                };\n                _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule].forEach({\n                    \"Sidebar.useEffect\": (submenu)=>{\n                        if (submenu.type === 'group' && isGroupActive(activeModule, submenu.items)) {\n                            newExpandedGroups[submenu.id] = true;\n                        }\n                    }\n                }[\"Sidebar.useEffect\"]);\n                setExpandedGroups(newExpandedGroups);\n            }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Sidebar.useEffect\"], [\n        activeModule,\n        pathname,\n        isGroupActive\n    ]);\n    // Verificar se um item de submenu tem permissão para ser exibido\n    const hasPermissionForSubmenuItem = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Sidebar.useCallback[hasPermissionForSubmenuItem]\": (moduleId, submenuId)=>{\n            return hasPermissionForSubmenu(moduleId, submenuId);\n        }\n    }[\"Sidebar.useCallback[hasPermissionForSubmenuItem]\"], [\n        hasPermissionForSubmenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col \".concat(isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'),\n        \"aria-label\": \"Navega\\xe7\\xe3o lateral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomScrollArea__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"flex-1 p-5\",\n                moduleColor: activeModule,\n                children: [\n                    activeModuleObject && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModuleTitle, {\n                        moduleId: activeModule,\n                        title: activeModuleTitle,\n                        icon: ModuleIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        \"aria-labelledby\": \"sidebar-heading\",\n                        children: activeModule && ((_moduleSubmenus_activeModule = _app_dashboard_components__WEBPACK_IMPORTED_MODULE_3__.moduleSubmenus[activeModule]) === null || _moduleSubmenus_activeModule === void 0 ? void 0 : _moduleSubmenus_activeModule.map((submenu)=>{\n                            // Verificar se é um grupo ou um item individual\n                            if (submenu.type === 'group') {\n                                // Verificar se algum item do grupo tem permissão para ser exibido\n                                const hasAnyPermission = submenu.items.some((item)=>hasPermissionForSubmenuItem(activeModule, item.id));\n                                if (!hasAnyPermission) {\n                                    return null; // Não renderizar o grupo se nenhum item tiver permissão\n                                }\n                                const isGroupExpanded = expandedGroups[submenu.id] || false;\n                                const isAnyItemActive = isGroupActive(activeModule, submenu.items);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>toggleGroup(submenu.id),\n                                            className: \"\\n                      group w-full flex items-center justify-between px-4 py-2.5 rounded-lg transition-all duration-300\\n                      \".concat(isAnyItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark font-medium\") : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                    \"),\n                                            \"aria-expanded\": isGroupExpanded,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: submenu.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-500 dark:text-gray-400\",\n                                                    children: isGroupExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        size: 18,\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isGroupExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4 pl-2 border-l border-gray-200 dark:border-gray-700 space-y-1\",\n                                            children: submenu.items.map((item)=>{\n                                                const isItemActive = isSubmenuActive(activeModule, item.id);\n                                                // Verificar permissão antes de renderizar o item\n                                                if (!hasPermissionForSubmenuItem(activeModule, item.id)) {\n                                                    return null; // Não renderizar se não tiver permissão\n                                                }\n                                                // Verificar se o item está em construção\n                                                const itemKey = \"\".concat(activeModule, \".\").concat(item.id);\n                                                const isItemUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_8__.isUnderConstruction)(activeModule, item.id);\n                                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_8__.getConstructionMessage)(activeModule, item.id);\n                                                // Estilo comum para os itens\n                                                const itemClassName = \"\\n                          group w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300\\n                          \".concat(isItemActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                               bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                               shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n                        \");\n                                                // Se estiver em construção, usar o ConstructionButton\n                                                if (isItemUnderConstruction) {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_7__.ConstructionButton, {\n                                                        className: itemClassName,\n                                                        \"aria-current\": isItemActive ? 'page' : undefined,\n                                                        title: constructionMessage.title,\n                                                        content: constructionMessage.content,\n                                                        icon: constructionMessage.icon,\n                                                        position: \"right\",\n                                                        children: [\n                                                            item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"\\n                                  \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                                \"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                    size: 18,\n                                                                    \"aria-hidden\": \"true\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 35\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 33\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 29\n                                                    }, undefined);\n                                                }\n                                                // Se não estiver em construção, usar o botão normal\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleModuleSubmenuClick(activeModule, item.id),\n                                                    className: itemClassName,\n                                                    \"aria-current\": isItemActive ? 'page' : undefined,\n                                                    children: [\n                                                        item.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"\\n                                \".concat(isItemActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                              \"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 18,\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 33\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 31\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-left text-sm \".concat(isItemActive ? 'dark:text-white' : ''),\n                                                            children: item.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 246,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 17\n                                }, undefined);\n                            } else {\n                                // Renderização de itens individuais (não agrupados)\n                                const isActive = isSubmenuActive(activeModule, submenu.id);\n                                // Verificar permissão antes de renderizar o item\n                                if (!hasPermissionForSubmenu(activeModule, submenu.id)) {\n                                    return null; // Não renderizar se não tiver permissão\n                                }\n                                // Verificar se o submenu está em construção\n                                const submenuKey = \"\".concat(activeModule, \".\").concat(submenu.id);\n                                const isSubmenuUnderConstruction = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_8__.isUnderConstruction)(activeModule, submenu.id);\n                                const constructionMessage = (0,_utils_constructionUtils__WEBPACK_IMPORTED_MODULE_8__.getConstructionMessage)(activeModule, submenu.id);\n                                // Estilo comum para ambos os tipos de botões\n                                const buttonClassName = \"\\n                group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300\\n                \".concat(isActive ? \"rounded-xl border border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n                     bg-module-\").concat(activeModule, \"-bg dark:bg-gray-700 dark:bg-opacity-90\\n                     shadow-md dark:shadow-black/20\") : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700', \"\\n              \");\n                                // Se estiver em construção, usar o ConstructionButton\n                                if (isSubmenuUnderConstruction) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_construction__WEBPACK_IMPORTED_MODULE_7__.ConstructionButton, {\n                                        className: buttonClassName,\n                                        \"aria-current\": isActive ? 'page' : undefined,\n                                        title: constructionMessage.title,\n                                        content: constructionMessage.content,\n                                        icon: constructionMessage.icon,\n                                        position: \"right\",\n                                        children: [\n                                            submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                    size: 20,\n                                                    \"aria-hidden\": \"true\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 368,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                                children: submenu.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 380,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, submenu.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                        lineNumber: 358,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }\n                                // Se não estiver em construção, usar o botão normal\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleModuleSubmenuClick(activeModule, submenu.id),\n                                    className: buttonClassName,\n                                    \"aria-current\": isActive ? 'page' : undefined,\n                                    children: [\n                                        submenu.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\\n                      \".concat(isActive ? \"text-module-\".concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\") : \"text-gray-500 dark:text-gray-400 group-hover:text-module-\".concat(activeModule, \"-icon dark:group-hover:text-module-\").concat(activeModule, \"-icon-dark transition-colors duration-200\"), \"\\n                    \"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(submenu.icon, {\n                                                size: 20,\n                                                \"aria-hidden\": \"true\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                                lineNumber: 400,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 394,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium text-left \".concat(isActive ? 'dark:text-white' : ''),\n                                            children: submenu.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                            lineNumber: 406,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, submenu.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, undefined);\n                            }\n                        }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-5 border-t border-gray-100 dark:border-gray-700 mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleBackToModules,\n                    className: \"\\n            group w-full py-3 px-4 rounded-lg flex items-center gap-3\\n            border-2 border-module-\".concat(activeModule, \"-border dark:border-module-\").concat(activeModule, \"-border-dark\\n            bg-transparent dark:bg-gray-800 hover:bg-module-\").concat(activeModule, \"-bg/10 dark:hover:bg-module-\").concat(activeModule, \"-bg-dark/10\\n            transition-all duration-300\\n          \"),\n                    \"aria-label\": \"Voltar para o dashboard principal\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"\\n            flex items-center justify-center w-8 h-8 rounded-full\\n            bg-module-\".concat(activeModule, \"-bg dark:bg-module-\").concat(activeModule, \"-bg-dark/70\\n            text-module-\").concat(activeModule, \"-icon dark:text-module-\").concat(activeModule, \"-icon-dark\\n            shadow-sm dark:shadow-md dark:shadow-black/20\\n            group-hover:scale-110 transition-transform duration-200\\n          \"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ChevronDown_ChevronLeft_ChevronRight_Construction_Hammer_HardHat_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 20,\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                                lineNumber: 433,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 426,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-800 dark:text-gray-200\",\n                            children: \"Voltar a Tela Inicial\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                            lineNumber: 435,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\dashboard\\\\Sidebar\\\\index.js\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"e0Bzrih02d515QuStoKQqw0RJk4=\", false, function() {\n    return [\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c1 = Sidebar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModuleTitle\");\n$RefreshReg$(_c1, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/Sidebar/index.js\n"));

/***/ })

});