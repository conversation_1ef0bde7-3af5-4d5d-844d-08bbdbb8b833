// Script para simular exatamente as chamadas da API
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function simulateApiCalls() {
  try {
    const companyId = '9c4195cf-fe76-4455-b515-44b07224706e';
    
    console.log('=== SIMULANDO CHAMADA GET /adminDashboard/plans ===');
    
    // Exatamente como o AdminDashboardController faz
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: companyId,
      },
      include: {
        modules: {
          where: {
            active: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            users: {
              where: {
                active: true,
              },
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (!subscription) {
      console.log('❌ Assinatura não encontrada');
      return;
    }

    // Calcular estatísticas de uso
    const currentUserCount = subscription.company.users.length;
    const userLimitUsage = subscription.userLimit > 0 
      ? Math.round((currentUserCount / subscription.userLimit) * 100)
      : 0;

    // Formatar dados da resposta (exatamente como o controller)
    const planData = {
      subscription: {
        id: subscription.id,
        status: subscription.status,
        billingCycle: subscription.billingCycle,
        pricePerMonth: parseFloat(subscription.pricePerMonth),
        userLimit: subscription.userLimit,
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        nextBillingDate: subscription.nextBillingDate,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
      },
      usage: {
        currentUsers: currentUserCount,
        userLimit: subscription.userLimit,
        userLimitUsage: userLimitUsage,
        availableUsers: Math.max(0, subscription.userLimit - currentUserCount),
      },
      modules: subscription.modules.map(module => ({
        id: module.id,
        moduleType: module.moduleType,
        pricePerMonth: parseFloat(module.pricePerMonth),
        addedAt: module.addedAt,
      })),
      company: {
        id: subscription.company.id,
        name: subscription.company.name,
      },
    };

    console.log('Resposta da API:');
    console.log(JSON.stringify(planData, null, 2));

    console.log('\n=== VERIFICAÇÃO DETALHADA DOS MÓDULOS ===');
    console.log(`Total de módulos retornados: ${planData.modules.length}`);
    
    planData.modules.forEach(module => {
      console.log(`- ${module.moduleType}: R$ ${module.pricePerMonth}`);
    });

    // Verificar se FINANCIAL está na lista
    const financialModule = planData.modules.find(m => m.moduleType === 'FINANCIAL');
    if (financialModule) {
      console.log('\n❌ PROBLEMA: Módulo FINANCIAL ainda aparece na resposta!');
      console.log('Detalhes do módulo:', financialModule);
      
      // Verificar diretamente no banco
      const directCheck = await prisma.subscriptionModule.findUnique({
        where: { id: financialModule.id }
      });
      
      console.log('Verificação direta no banco:');
      console.log(`- ID: ${directCheck.id}`);
      console.log(`- Tipo: ${directCheck.moduleType}`);
      console.log(`- Ativo: ${directCheck.active}`);
      
    } else {
      console.log('\n✅ Módulo FINANCIAL não aparece na resposta (correto)');
    }

    console.log('\n=== SIMULANDO REMOÇÃO DO MÓDULO RH ===');
    
    // Encontrar módulo RH
    const rhModule = planData.modules.find(m => m.moduleType === 'RH');
    if (rhModule) {
      console.log(`Removendo módulo RH (ID: ${rhModule.id})`);
      
      // Desativar módulo
      await prisma.subscriptionModule.update({
        where: { id: rhModule.id },
        data: { active: false }
      });
      
      // Atualizar preço
      const newPrice = planData.subscription.pricePerMonth - rhModule.pricePerMonth;
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: { pricePerMonth: newPrice }
      });
      
      console.log(`✅ Módulo RH removido. Novo preço: R$ ${newPrice}`);
      
      // Verificar novamente
      console.log('\n=== VERIFICAÇÃO APÓS REMOÇÃO ===');
      
      const afterRemoval = await prisma.subscription.findFirst({
        where: { companyId: companyId },
        include: {
          modules: {
            where: { active: true }
          }
        }
      });
      
      console.log('Módulos ativos após remoção:');
      afterRemoval.modules.forEach(m => {
        console.log(`- ${m.moduleType}: R$ ${m.pricePerMonth}`);
      });
      
      const rhStillThere = afterRemoval.modules.find(m => m.moduleType === 'RH');
      if (rhStillThere) {
        console.log('❌ PROBLEMA: Módulo RH ainda aparece!');
      } else {
        console.log('✅ Módulo RH removido com sucesso');
      }
    }

  } catch (error) {
    console.error('Erro na simulação:', error);
  } finally {
    await prisma.$disconnect();
  }
}

simulateApiCalls();
