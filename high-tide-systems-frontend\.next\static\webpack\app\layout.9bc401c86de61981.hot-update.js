"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"259435a5da80\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFByb2dyYW1hw6fDo29cXGhpZ2gtdGlkZS1zeXN0ZW1zLWZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyNTk0MzVhNWRhODBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/navigation/QuickNav.js":
/*!***********************************************!*\
  !*** ./src/components/navigation/QuickNav.js ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart,Bookmark,Calendar,ChevronRight,Clock,CreditCard,FileText,Home,Info,LayoutDashboard,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Mapeamento de módulos e suas rotas\nconst navigationItems = [\n    {\n        category: 'Módulos',\n        items: [\n            {\n                title: 'Dashboard',\n                description: 'Visão geral do sistema',\n                path: '/dashboard',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 39,\n                    columnNumber: 15\n                }, undefined),\n                permission: null // Todos têm acesso\n            },\n            {\n                title: 'Agendamento',\n                description: 'Gerenciar calendário e agendamentos',\n                path: '/dashboard/scheduler/calendar',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 46,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.calendar.view'\n            },\n            {\n                title: 'Pessoas',\n                description: 'Gerenciar pacientes e contatos',\n                path: '/dashboard/people/persons',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 53,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.persons.view'\n            },\n            {\n                title: 'Administração',\n                description: 'Configurações do sistema',\n                path: '/dashboard/admin/introduction',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 60,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view'\n            }\n        ]\n    },\n    {\n        category: 'Agendamento',\n        items: [\n            {\n                title: 'Introdução',\n                description: 'Visão geral do módulo de agendamento',\n                path: '/dashboard/scheduler/introduction',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 72,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduler.dashboard.view'\n            },\n            {\n                title: 'Calendário',\n                description: 'Visualizar e gerenciar agendamentos',\n                path: '/dashboard/scheduler/calendar',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 79,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.calendar.view'\n            },\n            {\n                title: 'Relatório',\n                description: 'Relatórios e estatísticas de agendamentos',\n                path: '/dashboard/scheduler/appointments-report',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 86,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.appointments-report.view'\n            },\n            {\n                title: 'Horários de Trabalho',\n                description: 'Configurar horários de atendimento',\n                path: '/dashboard/scheduler/working-hours',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 93,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.working-hours.view'\n            },\n            {\n                title: 'Tipos de Serviço',\n                description: 'Gerenciar tipos de serviço disponíveis',\n                path: '/dashboard/scheduler/service-types',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 100,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.service-types.view'\n            },\n            {\n                title: 'Locais',\n                description: 'Gerenciar locais de atendimento',\n                path: '/dashboard/scheduler/locations',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 107,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.locations.view'\n            },\n            {\n                title: 'Dashboard',\n                description: 'Análise de agendamentos e estatísticas',\n                path: '/dashboard/scheduler/appointments-dashboard',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 114,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.appointments-dashboard.view'\n            }\n        ]\n    },\n    {\n        category: 'Pessoas',\n        items: [\n            {\n                title: 'Introdução',\n                description: 'Visão geral do módulo de pessoas',\n                path: '/dashboard/people/introduction',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 126,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.dashboard.view'\n            },\n            {\n                title: 'Clientes',\n                description: 'Gerenciar clientes e contas',\n                path: '/dashboard/people/clients',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 133,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.clients.view'\n            },\n            {\n                title: 'Pacientes',\n                description: 'Gerenciar cadastro de pacientes',\n                path: '/dashboard/people/persons',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 140,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.persons.view'\n            },\n            {\n                title: 'Convênios',\n                description: 'Gerenciar convênios e planos',\n                path: '/dashboard/people/insurances',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 147,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.insurances.view'\n            },\n            {\n                title: 'Limites de Convênio',\n                description: 'Configurar limites de atendimento por convênio',\n                path: '/dashboard/people/insurance-limits',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 154,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.insurance-limits.view'\n            },\n            {\n                title: 'Dashboard',\n                description: 'Análise e estatísticas de pessoas',\n                path: '/dashboard/people/dashboard',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 161,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.dashboard.view'\n            }\n        ]\n    },\n    {\n        category: 'Administração',\n        items: [\n            {\n                title: 'Usuários',\n                description: 'Gerenciar usuários do sistema',\n                path: '/dashboard/admin/users',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 173,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.users.view'\n            },\n            {\n                title: 'Profissões',\n                description: 'Gerenciar profissões e grupos',\n                path: '/dashboard/admin/professions',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 180,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.professions.view'\n            },\n            {\n                title: 'Configurações',\n                description: 'Configurações gerais do sistema',\n                path: '/dashboard/admin/settings',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 187,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.config.edit'\n            }\n        ]\n    },\n    {\n        category: 'Ações Rápidas',\n        items: [\n            {\n                title: 'Novo Agendamento',\n                description: 'Criar um novo agendamento',\n                path: '/dashboard/scheduler/calendar?action=new',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 199,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.calendar.edit'\n            },\n            {\n                title: 'Novo Paciente',\n                description: 'Cadastrar um novo paciente',\n                path: '/dashboard/people/persons?action=new',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 206,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.persons.edit'\n            }\n        ]\n    },\n    {\n        category: 'Desenvolvimento',\n        items: [\n            {\n                title: 'Sistema de Design',\n                description: 'Documentação e exemplos de componentes UI',\n                path: '/design-system',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 218,\n                    columnNumber: 15\n                }, undefined),\n                permission: null // Todos têm acesso\n            }\n        ]\n    }\n];\nconst QuickNav = ()=>{\n    _s();\n    const { isOpen, closeQuickNav, searchTerm, setSearchTerm, navigationHistory, addToHistory } = (0,_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav)();\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const searchInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    // Filtrar itens com base no termo de pesquisa\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuickNav.useEffect\": ()=>{\n            if (!isOpen) return;\n            // Filtrar itens com base no termo de pesquisa e permissões\n            const filtered = [];\n            // Adicionar histórico de navegação se houver termo de pesquisa\n            if (navigationHistory.length > 0) {\n                const historyItems = navigationHistory.filter({\n                    \"QuickNav.useEffect.historyItems\": (item)=>item.title.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())\n                }[\"QuickNav.useEffect.historyItems\"]);\n                if (historyItems.length > 0) {\n                    filtered.push({\n                        category: 'Recentes',\n                        items: historyItems\n                    });\n                }\n            }\n            // Filtrar itens de navegação\n            navigationItems.forEach({\n                \"QuickNav.useEffect\": (category)=>{\n                    const items = category.items.filter({\n                        \"QuickNav.useEffect.items\": (item)=>// Verificar permissão\n                            (item.permission === null || can(item.permission)) && // Verificar termo de pesquisa\n                            (searchTerm === '' || item.title.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))\n                    }[\"QuickNav.useEffect.items\"]);\n                    if (items.length > 0) {\n                        filtered.push({\n                            category: category.category,\n                            items: items\n                        });\n                    }\n                }\n            }[\"QuickNav.useEffect\"]);\n            setFilteredItems(filtered);\n            setSelectedIndex(0);\n        }\n    }[\"QuickNav.useEffect\"], [\n        isOpen,\n        searchTerm,\n        navigationHistory,\n        can\n    ]);\n    // Focar no input de pesquisa quando o QuickNav é aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuickNav.useEffect\": ()=>{\n            if (isOpen && searchInputRef.current) {\n                setTimeout({\n                    \"QuickNav.useEffect\": ()=>{\n                        searchInputRef.current.focus();\n                    }\n                }[\"QuickNav.useEffect\"], 100);\n            }\n        }\n    }[\"QuickNav.useEffect\"], [\n        isOpen\n    ]);\n    // Lidar com navegação por teclado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuickNav.useEffect\": ()=>{\n            if (!isOpen) return;\n            const handleKeyDown = {\n                \"QuickNav.useEffect.handleKeyDown\": (e)=>{\n                    // Calcular o número total de itens\n                    const totalItems = filteredItems.reduce({\n                        \"QuickNav.useEffect.handleKeyDown.totalItems\": (acc, category)=>acc + category.items.length\n                    }[\"QuickNav.useEffect.handleKeyDown.totalItems\"], 0);\n                    switch(e.key){\n                        case 'ArrowDown':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"QuickNav.useEffect.handleKeyDown\": (prev)=>(prev + 1) % totalItems\n                            }[\"QuickNav.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'ArrowUp':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"QuickNav.useEffect.handleKeyDown\": (prev)=>(prev - 1 + totalItems) % totalItems\n                            }[\"QuickNav.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'Enter':\n                            e.preventDefault();\n                            handleSelectItem();\n                            break;\n                    }\n                }\n            }[\"QuickNav.useEffect.handleKeyDown\"];\n            window.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"QuickNav.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"QuickNav.useEffect\"];\n        }\n    }[\"QuickNav.useEffect\"], [\n        isOpen,\n        filteredItems,\n        selectedIndex\n    ]);\n    // Função para selecionar um item\n    const handleSelectItem = ()=>{\n        // Encontrar o item selecionado\n        let currentIndex = 0;\n        let selectedItem = null;\n        for (const category of filteredItems){\n            for (const item of category.items){\n                if (currentIndex === selectedIndex) {\n                    selectedItem = item;\n                    break;\n                }\n                currentIndex++;\n            }\n            if (selectedItem) break;\n        }\n        if (selectedItem) {\n            // Adicionar ao histórico\n            addToHistory(selectedItem);\n            // Navegar para a rota\n            router.push(selectedItem.path);\n            // Fechar o QuickNav\n            closeQuickNav();\n        }\n    };\n    // Função para lidar com clique em um item\n    const handleItemClick = (item)=>{\n        // Adicionar ao histórico\n        addToHistory(item);\n        // Navegar para a rota\n        router.push(item.path);\n        // Fechar o QuickNav\n        closeQuickNav();\n    };\n    // Não renderizar nada se o QuickNav não estiver aberto\n    if (!isOpen) return null;\n    // Renderizar o QuickNav usando um portal\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_4__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[13000] flex items-start justify-center pt-[15vh] bg-black/50 backdrop-blur-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700\",\n            onClick: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ctrl + K\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                lineNumber: 371,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500\",\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: searchInputRef,\n                                    type: \"text\",\n                                    placeholder: \"Pesquisar...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 bg-transparent border-none focus:outline-none text-gray-800 dark:text-gray-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closeQuickNav,\n                            className: \"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 369,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[60vh] overflow-y-auto\",\n                    children: filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Nenhum resultado encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 398,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                        lineNumber: 397,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2\",\n                        children: filteredItems.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xs font-semibold text-gray-500 dark:text-gray-400 px-3 py-2\",\n                                        children: category.category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                        lineNumber: 404,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: category.items.map((item, itemIndex)=>{\n                                            // Calcular o índice global do item\n                                            let globalIndex = 0;\n                                            for(let i = 0; i < categoryIndex; i++){\n                                                globalIndex += filteredItems[i].items.length;\n                                            }\n                                            globalIndex += itemIndex;\n                                            // Verificar se este é o item selecionado\n                                            const isSelected = globalIndex === selectedIndex;\n                                            // Determinar a cor do ícone com base na categoria\n                                            let iconColorClass = 'text-gray-400';\n                                            if (category.category === 'Agendamento') {\n                                                iconColorClass = 'text-purple-500 dark:text-purple-400';\n                                            } else if (category.category === 'Pessoas') {\n                                                iconColorClass = 'text-orange-500 dark:text-orange-400';\n                                            } else if (category.category === 'Administração') {\n                                                iconColorClass = 'text-red-500 dark:text-red-400';\n                                            } else if (category.category === 'Ações Rápidas') {\n                                                iconColorClass = 'text-blue-500 dark:text-blue-400';\n                                            } else if (category.category === 'Recentes') {\n                                                iconColorClass = 'text-gray-400 dark:text-gray-500';\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2 rounded-lg flex items-center gap-3 cursor-pointer transition-colors \".concat(isSelected ? 'bg-gray-100 dark:bg-gray-700' : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'),\n                                                onClick: ()=>handleItemClick(item),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(iconColorClass, \" bg-gray-100 dark:bg-gray-700\"),\n                                                        children: category.category === 'Recentes' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 31\n                                                        }, undefined) : item.icon || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 44\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-800 dark:text-gray-200 truncate\",\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                                children: item.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart_Bookmark_Calendar_ChevronRight_Clock_CreditCard_FileText_Home_Info_LayoutDashboard_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, \"\".concat(item.path, \"-\").concat(itemIndex), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                lineNumber: 435,\n                                                columnNumber: 25\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                        lineNumber: 408,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, categoryIndex, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                lineNumber: 403,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                        lineNumber: 401,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 text-xs text-gray-500 dark:text-gray-400 flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600\",\n                                            children: \"↑\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600\",\n                                            children: \"↓\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1\",\n                                            children: \"navegar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600\",\n                                            children: \"Enter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1\",\n                                            children: \"selecionar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 485,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600\",\n                                            children: \"Esc\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1\",\n                                            children: \"fechar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"Pressione \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Ctrl+K\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 497,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" ou \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 497,\n                                    columnNumber: 70\n                                }, undefined),\n                                \" para abrir\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, undefined), document.body);\n};\n_s(QuickNav, \"aBMbFgnRmFhShupsprkeLo0SrL8=\", false, function() {\n    return [\n        _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions\n    ];\n});\n_c = QuickNav;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuickNav);\nvar _c;\n$RefreshReg$(_c, \"QuickNav\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/QuickNav.js\n"));

/***/ })

});