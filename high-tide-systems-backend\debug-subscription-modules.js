// Script para debugar módulos da assinatura
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function debugSubscriptionModules() {
  try {
    const companyId = '9c4195cf-fe76-4455-b515-44b07224706e';
    
    console.log(`Debugando assinatura da empresa: ${companyId}`);
    
    // Buscar informações completas da assinatura
    const subscription = await prisma.subscription.findFirst({
      where: {
        companyId: companyId,
      },
      include: {
        modules: true, // Incluir TODOS os módulos, ativos e inativos
        company: {
          select: {
            id: true,
            name: true,
            users: {
              where: {
                active: true,
              },
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    if (!subscription) {
      console.log('Assinatura não encontrada!');
      return;
    }

    console.log('\n=== INFORMAÇÕES DA ASSINATURA ===');
    console.log(`ID: ${subscription.id}`);
    console.log(`Empresa: ${subscription.company.name}`);
    console.log(`Status: ${subscription.status}`);
    console.log(`Ativo: ${subscription.active}`);
    console.log(`Usuários atuais: ${subscription.company.users.length}`);
    console.log(`Limite de usuários: ${subscription.userLimit}`);
    console.log(`Preço mensal: R$ ${subscription.pricePerMonth}`);

    console.log('\n=== MÓDULOS DA ASSINATURA ===');
    subscription.modules.forEach(module => {
      console.log(`\nMódulo: ${module.moduleType}`);
      console.log(`  - ID: ${module.id}`);
      console.log(`  - Ativo: ${module.active}`);
      console.log(`  - Preço: R$ ${module.pricePerMonth}`);
      console.log(`  - Adicionado em: ${module.addedAt}`);
    });

    // Verificar especificamente o módulo FINANCIAL
    const financialModule = subscription.modules.find(m => m.moduleType === 'FINANCIAL');
    
    console.log('\n=== ANÁLISE DO MÓDULO FINANCIAL ===');
    if (financialModule) {
      console.log(`Módulo FINANCIAL encontrado:`);
      console.log(`  - ID: ${financialModule.id}`);
      console.log(`  - Ativo: ${financialModule.active}`);
      console.log(`  - Preço: R$ ${financialModule.pricePerMonth}`);
      
      if (!financialModule.active) {
        console.log('\n⚠️  PROBLEMA: Módulo FINANCIAL está INATIVO!');
        console.log('Reativando módulo...');
        
        await prisma.subscriptionModule.update({
          where: { id: financialModule.id },
          data: { active: true }
        });
        
        console.log('✅ Módulo FINANCIAL reativado!');
      } else {
        console.log('✅ Módulo FINANCIAL está ativo');
      }
    } else {
      console.log('❌ Módulo FINANCIAL não encontrado na assinatura');
    }

    // Verificar cache do Redis se existir
    console.log('\n=== VERIFICAÇÃO DE CACHE ===');
    console.log('Verificando se há problemas de cache...');
    
    // Buscar novamente após possível correção
    const updatedSubscription = await prisma.subscription.findFirst({
      where: { companyId: companyId },
      include: {
        modules: {
          where: { active: true }
        }
      }
    });

    console.log('\n=== MÓDULOS ATIVOS APÓS CORREÇÃO ===');
    updatedSubscription.modules.forEach(module => {
      console.log(`- ${module.moduleType}: R$ ${module.pricePerMonth}`);
    });

  } catch (error) {
    console.error('Erro ao debugar assinatura:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugSubscriptionModules();
