"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/app/dashboard/ClientHeader.js":
/*!*******************************************!*\
  !*** ./src/app/dashboard/ClientHeader.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,Clock,LogOut,Menu,Search,Settings,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(app-pages-browser)/./src/components/ThemeToggle.js\");\n/* harmony import */ var _config_appConfig__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/appConfig */ \"(app-pages-browser)/./src/config/appConfig.js\");\n/* harmony import */ var _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/people/services/personsService */ \"(app-pages-browser)/./src/app/modules/people/services/personsService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Header Component adaptado para clientes\nconst ClientHeader = (param)=>{\n    let { toggleSidebar, isSidebarOpen } = param;\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { openQuickNav } = (0,_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav)();\n    // Pegar primeira letra de cada nome para o avatar\n    const getInitials = ()=>{\n        var _user_login;\n        // Verificar se temos uma pessoa associada ao cliente\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            const fullName = user.persons[0].fullName;\n            const names = fullName.split(' ');\n            if (names.length === 1) return names[0].charAt(0);\n            return \"\".concat(names[0].charAt(0)).concat(names[names.length - 1].charAt(0));\n        }\n        // Fallback para o login do cliente\n        return (user === null || user === void 0 ? void 0 : (_user_login = user.login) === null || _user_login === void 0 ? void 0 : _user_login.charAt(0)) || 'C';\n    };\n    // Obter o nome completo da pessoa associada ao cliente ou o login do cliente\n    const getDisplayName = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0 && user.persons[0].fullName) {\n            return user.persons[0].fullName;\n        }\n        return (user === null || user === void 0 ? void 0 : user.login) || 'Cliente';\n    };\n    // Obter a URL da imagem de perfil da pessoa associada ao cliente\n    const getProfileImage = ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.persons) && user.persons.length > 0) {\n            // Primeiro tenta usar a URL completa se disponível\n            if (user.persons[0].profileImageFullUrl) {\n                return user.persons[0].profileImageFullUrl;\n            } else if (user.persons[0].profileImageUrl) {\n                return _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_7__.personsService.getProfileImageUrl(user.persons[0].id, user.persons[0].profileImageUrl);\n            }\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9999]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: toggleSidebar,\n                        className: \"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors\",\n                        \"aria-label\": isSidebarOpen ? \"Fechar menu lateral\" : \"Abrir menu lateral\",\n                        children: isSidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 67,\n                            columnNumber: 28\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            size: 22,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 67,\n                            columnNumber: 65\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/logo_horizontal_sem_fundo.png\",\n                                    alt: \"High Tide Logo\",\n                                    className: \"h-10 mr-2.5 dark:invert dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono\",\n                                    children: _config_appConfig__WEBPACK_IMPORTED_MODULE_6__.APP_VERSION\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openQuickNav,\n                        className: \"flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors\",\n                        \"aria-label\": \"Abrir pesquisa r\\xe1pida\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 18,\n                                className: \"text-gray-400 dark:text-gray-500\",\n                                \"aria-hidden\": \"true\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Pesquisar...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Ctrl + K\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/calendar'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\",\n                        \"aria-label\": \"Calend\\xe1rio\",\n                        title: \"Ver calend\\xe1rio\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors\",\n                        \"aria-label\": \"Meus Agendamentos\",\n                        title: \"Ver meus agendamentos\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 20,\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 border-l border-gray-200 dark:border-gray-700 mx-1\",\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                \"aria-expanded\": \"false\",\n                                \"aria-haspopup\": \"true\",\n                                \"aria-label\": \"Menu do usu\\xe1rio\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-9 w-9 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center font-medium text-purple-600 dark:text-purple-400 overflow-hidden\",\n                                        children: getProfileImage() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: getProfileImage(),\n                                            alt: \"Foto de perfil de \".concat(getDisplayName()),\n                                            className: \"h-10 w-10 rounded-full object-cover\",\n                                            onError: (e)=>{\n                                                e.target.onerror = null;\n                                                e.target.style.display = 'none';\n                                                e.target.parentNode.innerHTML = getInitials();\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined) : getInitials()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 bg-purple-50 dark:bg-purple-900/30\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: 10,\n                                                        className: \"mr-1\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Cliente\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-gray-400 dark:text-gray-500 hidden md:block\",\n                                        \"aria-hidden\": \"true\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-[10000] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right\",\n                                role: \"menu\",\n                                \"aria-orientation\": \"vertical\",\n                                \"aria-labelledby\": \"user-menu-button\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-800 dark:text-gray-200\",\n                                                children: getDisplayName()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"pt-1 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/profile'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meu Perfil\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/people/persons'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Minhas Pessoas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/dashboard/scheduler/appointments-report'),\n                                                className: \"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                                role: \"menuitem\",\n                                                children: \"Meus Agendamentos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: logout,\n                                                className: \"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors\",\n                                                role: \"menuitem\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_Clock_LogOut_Menu_Search_Settings_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"mr-2\",\n                                                        \"aria-hidden\": \"true\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Sair do Sistema\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\ClientHeader.js\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientHeader, \"lxBQFzzV6dgk+z4JHIb9l7reeX8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav\n    ];\n});\n_c = ClientHeader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientHeader);\nvar _c;\n$RefreshReg$(_c, \"ClientHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/ClientHeader.js\n"));

/***/ })

});