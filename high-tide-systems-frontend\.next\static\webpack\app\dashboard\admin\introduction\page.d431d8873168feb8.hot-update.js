"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/introduction/page",{

/***/ "(app-pages-browser)/./src/components/users/RoleModal.js":
/*!*******************************************!*\
  !*** ./src/components/users/RoleModal.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Shield,ShieldCheck,UserCog,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Shield,ShieldCheck,UserCog,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Shield,ShieldCheck,UserCog,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Shield,ShieldCheck,UserCog,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Shield,ShieldCheck,UserCog,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Loader2,Shield,ShieldCheck,UserCog,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst RoleModal = (param)=>{\n    let { isOpen, onClose, user, onSuccess } = param;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Configuração dos roles disponíveis\n    const roles = [\n        {\n            id: \"SYSTEM_ADMIN\",\n            name: \"Administrador do Sistema\",\n            description: \"Acesso completo a todas as funcionalidades e empresas\",\n            icon: _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800/50\",\n            requiresSystemAdmin: true\n        },\n        {\n            id: \"COMPANY_ADMIN\",\n            name: \"Administrador da Empresa\",\n            description: \"Acesso completo às funcionalidades dentro da empresa\",\n            icon: _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: \"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50\",\n            requiresSystemAdmin: false\n        },\n        {\n            id: \"EMPLOYEE\",\n            name: \"Funcionário\",\n            description: \"Acesso limitado às funcionalidades atribuídas\",\n            icon: _barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: \"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50\",\n            requiresSystemAdmin: false\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoleModal.useEffect\": ()=>{\n            if (user && isOpen) {\n                setSelectedRole(user.role || \"EMPLOYEE\");\n            }\n        }\n    }[\"RoleModal.useEffect\"], [\n        user,\n        isOpen\n    ]);\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    const isCompanyAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"COMPANY_ADMIN\";\n    const handleRoleChange = (roleId)=>{\n        setSelectedRole(roleId);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__.userService.updateRole(user.id, selectedRole);\n            onSuccess();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao atualizar role:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao atualizar função do usuário\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-2xl w-full max-h-[90vh] flex flex-col z-[11050]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-neutral-800 dark:text-white\",\n                                        children: \"Alterar Fun\\xe7\\xe3o do Usu\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto p-6\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-medium text-neutral-800 dark:text-white mb-1\",\n                                        children: user === null || user === void 0 ? void 0 : user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-600 dark:text-gray-300\",\n                                        children: \"Selecione a fun\\xe7\\xe3o deste usu\\xe1rio no sistema:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: roles.map((role)=>{\n                                    const isSelected = selectedRole === role.id;\n                                    const Icon = role.icon;\n                                    const isDisabled = role.requiresSystemAdmin && !isSystemAdmin;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 rounded-lg border \".concat(isSelected ? role.color : \"border-neutral-200 dark:border-gray-700\", \" \").concat(isDisabled ? \"opacity-70 cursor-not-allowed\" : \"cursor-pointer hover:border-primary-300 dark:hover:border-primary-700\"),\n                                        onClick: ()=>{\n                                            if (!isDisabled) {\n                                                handleRoleChange(role.id);\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"radio\",\n                                                        checked: isSelected,\n                                                        onChange: ()=>{},\n                                                        disabled: isDisabled,\n                                                        className: \"h-5 w-5 rounded-full border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                    className: \"h-5 w-5 \".concat(isSelected ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                                    children: [\n                                                                        role.name,\n                                                                        role.id === \"SYSTEM_ADMIN\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded\",\n                                                                            children: \"Acesso Total\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                                            lineNumber: 149,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                            lineNumber: 144,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                                            children: role.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        role.requiresSystemAdmin && !isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    size: 12\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Apenas administradores do sistema podem conceder este acesso\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, role.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, undefined),\n                            !isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-amber-500 dark:text-amber-400 flex-shrink-0 mt-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-amber-800 dark:text-amber-300\",\n                                                    children: \"Restri\\xe7\\xf5es de acesso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-amber-700 dark:text-amber-400\",\n                                                    children: isCompanyAdmin ? \"Como Administrador da Empresa, você só pode gerenciar usuários dentro da sua empresa e não pode criar Administradores do Sistema.\" : \"Você tem permissões limitadas para alterar funções de usuários. Algumas opções podem não estar disponíveis.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onClose,\n                                className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                disabled: isLoading,\n                                children: \"Cancelar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSave,\n                                className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\",\n                                disabled: isLoading || selectedRole === (user === null || user === void 0 ? void 0 : user.role),\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 16,\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Salvando...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Loader2_Shield_ShieldCheck_UserCog_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Salvar Altera\\xe7\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\RoleModal.js\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RoleModal, \"aw1MKnIbJ7S7OQ6Z9gUZKh8DP7Q=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = RoleModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RoleModal);\nvar _c;\n$RefreshReg$(_c, \"RoleModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/RoleModal.js\n"));

/***/ })

});