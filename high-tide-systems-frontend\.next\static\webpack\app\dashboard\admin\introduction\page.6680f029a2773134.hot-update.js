"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/introduction/page",{

/***/ "(app-pages-browser)/./src/components/settings/EmailSettingsTab.js":
/*!*****************************************************!*\
  !*** ./src/components/settings/EmailSettingsTab.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Check,CheckCircle,Edit,Loader2,Lock,Mail,Plus,Send,Server,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst EmailSettingsTab = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [emailConfigs, setEmailConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [modalOpen, setModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testModalOpen, setTestModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingConfig, setEditingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testingConfig, setTestingConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [testEmail, setTestEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTesting, setIsTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingCompanies, setLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // For system admin, use the selected company, otherwise use the user's company\n    const companyId = isSystemAdmin ? selectedCompanyId : user === null || user === void 0 ? void 0 : user.companyId;\n    // Form state for new/edit email config\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        smtpHost: \"\",\n        smtpPort: \"587\",\n        smtpSecure: false,\n        smtpUser: \"\",\n        smtpPassword: \"\",\n        emailFromName: \"\",\n        emailFromAddress: \"\",\n        active: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmailSettingsTab.useEffect\": ()=>{\n            // If system admin, load the companies for the dropdown\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n        }\n    }[\"EmailSettingsTab.useEffect\"], [\n        isSystemAdmin\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EmailSettingsTab.useEffect\": ()=>{\n            if (companyId) {\n                loadEmailConfigs();\n            } else {\n                setEmailConfigs([]);\n            }\n        }\n    }[\"EmailSettingsTab.useEffect\"], [\n        companyId\n    ]);\n    // Load companies for system admin\n    const loadCompanies = async ()=>{\n        setLoadingCompanies(true);\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/companies\", {\n                params: {\n                    active: true,\n                    limit: 100 // Get all active companies\n                }\n            });\n            setCompanies(response.data.companies || []);\n            // If there are companies, select the first one by default\n            if (response.data.companies && response.data.companies.length > 0) {\n                setSelectedCompanyId(response.data.companies[0].id);\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error loading companies:\", error);\n            setError(\"Falha ao carregar empresas: \" + (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"\"));\n        } finally{\n            setLoadingCompanies(false);\n        }\n    };\n    const loadEmailConfigs = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            let targetCompanyId = companyId;\n            // If system admin and no company selected, just show message\n            if (isSystemAdmin && !targetCompanyId) {\n                setEmailConfigs([]);\n                setIsLoading(false);\n                return;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/email-configs\", {\n                params: {\n                    companyId: targetCompanyId\n                }\n            });\n            setEmailConfigs(response.data.emailConfigs || []);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Error loading email configurations:\", err);\n            setError(\"Falha ao carregar configurações de email. \" + (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: type === 'checkbox' ? checked : value\n            }));\n    };\n    const resetForm = ()=>{\n        setFormData({\n            smtpHost: \"\",\n            smtpPort: \"587\",\n            smtpSecure: false,\n            smtpUser: \"\",\n            smtpPassword: \"\",\n            emailFromName: \"\",\n            emailFromAddress: \"\",\n            active: true\n        });\n        setEditingConfig(null);\n    };\n    const openAddModal = ()=>{\n        resetForm();\n        setModalOpen(true);\n    };\n    const openEditModal = (config)=>{\n        setFormData({\n            smtpHost: config.smtpHost,\n            smtpPort: config.smtpPort.toString(),\n            smtpSecure: config.smtpSecure,\n            smtpUser: config.smtpUser,\n            smtpPassword: \"\",\n            emailFromName: config.emailFromName,\n            emailFromAddress: config.emailFromAddress,\n            active: config.active\n        });\n        setEditingConfig(config);\n        setModalOpen(true);\n    };\n    const validateForm = ()=>{\n        // Basic validation\n        if (!formData.smtpHost) return \"Servidor SMTP é obrigatório\";\n        if (!formData.smtpPort) return \"Porta SMTP é obrigatória\";\n        if (!formData.smtpUser) return \"Usuário SMTP é obrigatório\";\n        if (!formData.emailFromName) return \"Nome do remetente é obrigatório\";\n        if (!formData.emailFromAddress) return \"Email do remetente é obrigatório\";\n        // Validate email format\n        if (!/^\\S+@\\S+\\.\\S+$/.test(formData.emailFromAddress)) {\n            return \"Formato de email inválido\";\n        }\n        // If editing and no password is provided, it's ok (we keep the old one)\n        if (!editingConfig && !formData.smtpPassword) {\n            return \"Senha SMTP é obrigatória\";\n        }\n        return null; // No errors\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate form\n        const validationError = validateForm();\n        if (validationError) {\n            setError(validationError);\n            return;\n        }\n        if (!companyId) {\n            setError(\"É necessário selecionar uma empresa\");\n            return;\n        }\n        setIsSaving(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            // Use the currently selected company\n            const targetCompanyId = companyId;\n            // Prepare data\n            const data = {\n                ...formData,\n                companyId: targetCompanyId,\n                smtpPort: parseInt(formData.smtpPort)\n            };\n            // If editing and no password provided, remove password field\n            if (editingConfig && !formData.smtpPassword) {\n                delete data.smtpPassword;\n            }\n            let response;\n            if (editingConfig) {\n                // Update existing config\n                response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.put(\"/email-configs/\".concat(editingConfig.id), data);\n                setSuccess(\"Configuração de email atualizada com sucesso!\");\n            } else {\n                // Create new config\n                response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post('/email-configs', data);\n                setSuccess(\"Configuração de email criada com sucesso!\");\n            }\n            // Reload configs\n            await loadEmailConfigs();\n            // Close modal\n            setModalOpen(false);\n            resetForm();\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Error saving email configuration:\", err);\n            setError(\"Falha ao salvar configuração de email: \" + (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"\"));\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleDelete = async (configId)=>{\n        if (!confirm(\"Tem certeza que deseja excluir esta configuração de email?\")) {\n            return;\n        }\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.delete(\"/email-configs/\".concat(configId));\n            setSuccess(\"Configuração de email excluída com sucesso!\");\n            await loadEmailConfigs();\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Error deleting email configuration:\", err);\n            setError(\"Falha ao excluir configuração de email: \" + (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleToggleActive = async (config)=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.patch(\"/email-configs/\".concat(config.id, \"/toggle-status\"));\n            setSuccess(\"Configura\\xe7\\xe3o de email \".concat(config.active ? 'desativada' : 'ativada', \" com sucesso!\"));\n            await loadEmailConfigs();\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Error toggling email configuration status:\", err);\n            setError(\"Falha ao alterar status da configuração: \" + (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"\"));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const openTestModal = (config)=>{\n        setTestingConfig(config);\n        setTestEmail((user === null || user === void 0 ? void 0 : user.email) || \"\");\n        setTestResult(null);\n        setTestModalOpen(true);\n    };\n    // Helper function to get company name\n    const getCompanyName = (id)=>{\n        if (!id) return \"\";\n        const company = companies.find((c)=>c.id === id);\n        return company ? company.name : \"\";\n    };\n    const handleTestEmail = async (e)=>{\n        e.preventDefault();\n        if (!testEmail || !/^\\S+@\\S+\\.\\S+$/.test(testEmail)) {\n            setError(\"Email de teste inválido\");\n            return;\n        }\n        setIsTesting(true);\n        setError(\"\");\n        setTestResult(null);\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.post(\"/email-configs/\".concat(testingConfig.id, \"/test\"), {\n                testEmail\n            });\n            setTestResult({\n                success: true,\n                message: \"Email enviado com sucesso!\"\n            });\n        } catch (err) {\n            var _err_response_data, _err_response, _err_response_data1, _err_response1;\n            console.error(\"Error testing email configuration:\", err);\n            setTestResult({\n                success: false,\n                message: \"Falha ao enviar email: \" + (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"Erro desconhecido\"),\n                details: ((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : (_err_response_data1 = _err_response1.data) === null || _err_response_data1 === void 0 ? void 0 : _err_response_data1.details) || null\n            });\n        } finally{\n            setIsTesting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium text-neutral-800 dark:text-gray-100 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Configura\\xe7\\xf5es de Email\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openAddModal,\n                        className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                        disabled: isLoading || !companyId,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Nova Configura\\xe7\\xe3o\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, undefined),\n            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-2\",\n                        htmlFor: \"companySelector\",\n                        children: \"Selecionar Empresa\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"companySelector\",\n                                value: selectedCompanyId,\n                                onChange: (e)=>setSelectedCompanyId(e.target.value),\n                                className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                disabled: loadingCompanies,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Selecione uma empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: company.id,\n                                            children: company.name\n                                        }, company.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined),\n                            loadingCompanies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"h-5 w-5 animate-spin text-primary-500 dark:text-primary-400 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                        children: \"Como administrador do sistema, voc\\xea pode gerenciar as configura\\xe7\\xf5es de email de qualquer empresa.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, undefined),\n            !selectedCompanyId && isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-md p-4 text-amber-800 dark:text-amber-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5 mr-2 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: \"Por favor, selecione uma empresa para configurar seus emails.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 382,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-md p-4 text-red-700 dark:text-red-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5 mr-2 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                    lineNumber: 394,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 393,\n                columnNumber: 9\n            }, undefined),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/50 rounded-md p-4 text-green-700 dark:text-green-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5 mr-2 mt-0.5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                    lineNumber: 403,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 402,\n                columnNumber: 9\n            }, undefined),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-6 w-6 animate-spin text-primary-500 dark:text-primary-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                    lineNumber: 413,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, undefined) : emailConfigs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 border border-neutral-100 dark:border-gray-700 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-12 w-12 mx-auto text-neutral-300 dark:text-gray-600 mb-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-neutral-800 dark:text-white mb-2\",\n                        children: \"Nenhuma configura\\xe7\\xe3o de email\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 418,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600 dark:text-gray-300 mb-6 max-w-md mx-auto\",\n                        children: \"Configure os par\\xe2metros de SMTP para enviar emails atrav\\xe9s do sistema, como notifica\\xe7\\xf5es, confirma\\xe7\\xf5es e relat\\xf3rios.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 419,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: openAddModal,\n                        className: \"inline-flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                        disabled: !companyId,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Adicionar Configura\\xe7\\xe3o de Email\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 416,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 border border-neutral-100 dark:border-gray-700 rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-neutral-50 dark:bg-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider\",\n                                            children: \"Nome do Remetente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 438,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider\",\n                                            children: \"Email do Remetente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 441,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider\",\n                                            children: \"Servidor SMTP\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 444,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider\",\n                                            children: \"A\\xe7\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 450,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                    lineNumber: 437,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 436,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\",\n                                children: emailConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-neutral-50 dark:hover:bg-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-800 dark:text-white\",\n                                                children: [\n                                                    config.emailFromName,\n                                                    isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-neutral-500 dark:text-gray-400 mt-1\",\n                                                        children: getCompanyName(config.companyId)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 458,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-gray-300\",\n                                                children: config.emailFromAddress\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 466,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1 text-neutral-400 dark:text-gray-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                config.smtpHost,\n                                                                \":\",\n                                                                config.smtpPort\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        config.smtpSecure && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 ml-2 text-green-500 dark:text-green-400\",\n                                                            title: \"SSL/TLS Enabled\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 469,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(config.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300\"),\n                                                    children: config.active ? \"Ativo\" : \"Inativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 478,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-end space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>openTestModal(config),\n                                                            className: \"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 px-2 py-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900/30\",\n                                                            title: \"Testar Configura\\xe7\\xe3o\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>openEditModal(config),\n                                                            className: \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white px-2 py-1 rounded hover:bg-neutral-50 dark:hover:bg-gray-700\",\n                                                            title: \"Editar\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleToggleActive(config),\n                                                            className: \"px-2 py-1 rounded hover:bg-neutral-50 dark:hover:bg-gray-700 \".concat(config.active ? \"text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300\" : \"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300\"),\n                                                            title: config.active ? \"Desativar\" : \"Ativar\",\n                                                            children: config.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 29\n                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDelete(config.id),\n                                                            className: \"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 px-2 py-1 rounded hover:bg-red-50 dark:hover:bg-red-900/30\",\n                                                            title: \"Excluir\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, config.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 457,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 435,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 433,\n                columnNumber: 9\n            }, undefined),\n            modalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50\",\n                        onClick: ()=>setModalOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 540,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-2xl w-full z-[11050]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            editingConfig ? \"Editar Configuração de Email\" : \"Nova Configuração de Email\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 544,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setModalOpen(false),\n                                        className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 552,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 548,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 543,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"smtpHost\",\n                                                        className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\",\n                                                        children: [\n                                                            \"Servidor SMTP \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 dark:text-red-400\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 35\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"smtpHost\",\n                                                        name: \"smtpHost\",\n                                                        placeholder: \"smtp.example.com\",\n                                                        value: formData.smtpHost,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 559,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"smtpPort\",\n                                                        className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\",\n                                                        children: [\n                                                            \"Porta SMTP \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 dark:text-red-400\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 32\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        id: \"smtpPort\",\n                                                        name: \"smtpPort\",\n                                                        placeholder: \"587\",\n                                                        value: formData.smtpPort,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                        children: \"Portas comuns: 25, 465 (SSL), 587 (TLS/STARTTLS)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 576,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"smtpUser\",\n                                                        className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\",\n                                                        children: [\n                                                            \"Usu\\xe1rio SMTP \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 dark:text-red-400\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 34\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"smtpUser\",\n                                                        name: \"smtpUser\",\n                                                        placeholder: \"<EMAIL>\",\n                                                        value: formData.smtpUser,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 596,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"smtpPassword\",\n                                                        className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\",\n                                                        children: [\n                                                            \"Senha SMTP \",\n                                                            !editingConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 dark:text-red-400\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 51\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        id: \"smtpPassword\",\n                                                        name: \"smtpPassword\",\n                                                        placeholder: editingConfig ? \"••••••••••••\" : \"Senha\",\n                                                        value: formData.smtpPassword,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                                        required: !editingConfig\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editingConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                        children: \"Deixe em branco para manter a senha atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 613,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"emailFromName\",\n                                                        className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\",\n                                                        children: [\n                                                            \"Nome do Remetente \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 dark:text-red-400\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 39\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        id: \"emailFromName\",\n                                                        name: \"emailFromName\",\n                                                        placeholder: \"High Tide\",\n                                                        value: formData.emailFromName,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"emailFromAddress\",\n                                                        className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\",\n                                                        children: [\n                                                            \"Email do Remetente \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 dark:text-red-400\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 40\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        id: \"emailFromAddress\",\n                                                        name: \"emailFromAddress\",\n                                                        placeholder: \"<EMAIL>\",\n                                                        value: formData.emailFromAddress,\n                                                        onChange: handleInputChange,\n                                                        className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 656,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                id: \"smtpSecure\",\n                                                                name: \"smtpSecure\",\n                                                                checked: formData.smtpSecure,\n                                                                onChange: handleInputChange,\n                                                                className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"smtpSecure\",\n                                                                className: \"ml-2 block text-sm text-neutral-700 dark:text-gray-200\",\n                                                                children: \"Usar SSL/TLS (geralmente para porta 465)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                                lineNumber: 679,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 ml-6 text-xs text-neutral-500 dark:text-gray-400\",\n                                                        children: \"Se desativado, STARTTLS ser\\xe1 utilizado quando dispon\\xedvel (recomendado para porta 587)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            id: \"active\",\n                                                            name: \"active\",\n                                                            checked: formData.active,\n                                                            onChange: handleInputChange,\n                                                            className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-400 border-neutral-300 dark:border-gray-600 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"active\",\n                                                            className: \"ml-2 block text-sm text-neutral-700 dark:text-gray-200\",\n                                                            children: \"Configura\\xe7\\xe3o ativa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 689,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 557,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8 flex justify-end gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setModalOpen(false),\n                                                className: \"px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 707,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: isSaving,\n                                                children: [\n                                                    isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 32\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: editingConfig ? \"Atualizar\" : \"Salvar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 715,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 706,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 542,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 539,\n                columnNumber: 9\n            }, undefined),\n            testModalOpen && testingConfig && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50\",\n                        onClick: ()=>setTestModalOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 732,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-md w-full z-[55]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Testar Configura\\xe7\\xe3o de Email\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 736,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setTestModalOpen(false),\n                                        className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 744,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 735,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"testEmail\",\n                                                className: \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\",\n                                                children: \"Email para Teste\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                id: \"testEmail\",\n                                                value: testEmail,\n                                                onChange: (e)=>setTestEmail(e.target.value),\n                                                placeholder: \"<EMAIL>\",\n                                                className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 dark:bg-gray-700 dark:text-white\",\n                                                disabled: isTesting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 753,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                children: \"Um email de teste ser\\xe1 enviado para este endere\\xe7o\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 p-3 bg-neutral-50 dark:bg-gray-700 border border-neutral-200 dark:border-gray-600 rounded-lg text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-neutral-800 dark:text-white mb-1\",\n                                                children: \"Configura\\xe7\\xe3o a ser testada:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 768,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-neutral-600 dark:text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Servidor:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 68\n                                                    }, undefined),\n                                                    \" \",\n                                                    testingConfig.smtpHost,\n                                                    \":\",\n                                                    testingConfig.smtpPort\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-neutral-600 dark:text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Usu\\xe1rio:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 68\n                                                    }, undefined),\n                                                    \" \",\n                                                    testingConfig.smtpUser\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 770,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-neutral-600 dark:text-gray-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Remetente:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" \",\n                                                    testingConfig.emailFromName,\n                                                    \" <\",\n                                                    testingConfig.emailFromAddress,\n                                                    \">\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 p-3 border rounded-lg \".concat(testResult.success ? \"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800/50 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 mt-0.5 text-green-500 dark:text-green-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                    lineNumber: 784,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2 mt-0.5 text-red-500 dark:text-red-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: testResult.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 789,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        testResult.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-xs font-mono overflow-auto max-h-40\",\n                                                            children: typeof testResult.details === 'object' ? JSON.stringify(testResult.details, null, 2) : testResult.details\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 791,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                    lineNumber: 788,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                            lineNumber: 782,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 777,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setTestModalOpen(false),\n                                                className: \"px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                                children: \"Fechar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 803,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTestEmail,\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: isTesting || !testEmail,\n                                                children: isTesting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"h-4 w-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 817,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Enviando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Check_CheckCircle_Edit_Loader2_Lock_Mail_Plus_Send_Server_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 822,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Enviar Email de Teste\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                                lineNumber: 810,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                        lineNumber: 802,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                                lineNumber: 748,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                        lineNumber: 734,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n                lineNumber: 731,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\EmailSettingsTab.js\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EmailSettingsTab, \"KVgAXHger2MzHYAJYxJRBz0meVU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = EmailSettingsTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmailSettingsTab);\nvar _c;\n$RefreshReg$(_c, \"EmailSettingsTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/EmailSettingsTab.js\n"));

/***/ })

});