// Test to verify UI updates automatically after module operations
async function testUIUpdate() {
    try {
        console.log('🔍 Testing UI automatic update...');
        
        const headers = {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjcwNjE1Mzc0LThkMjMtNGE0NC1iMmM5LWNhN2Y2MzNlYzAxZSIsImlhdCI6MTc0OTk5Nzc2MSwiaXNDbGllbnQiOmZhbHNlLCJtb2R1bGVzIjpbIkJBU0lDIiwiQURNSU4iLCJSSCIsIkZJTkFOQ0lBTCIsIlNDSEVEVUxJTkciXSwicm9sZSI6IlNZU1RFTV9BRE1JTiIsImV4cCI6MTc1MDA4NDE2MX0.9ZnELdYO8_AJbu9BYV8yh5ne6LhE1pWzh7ty0S3yp2c'
        };
        
        // Step 1: Get current modules
        console.log('📋 Step 1: Getting current modules...');
        const response1 = await fetch('http://localhost:5000/adminDashboard/plans?companyId=9c4195cf-fe76-4455-b515-44b07224706e', {
            headers
        });
        const data1 = await response1.json();
        console.log('Current modules:', data1.modules.map(m => m.moduleType));
        
        const hasRH = data1.modules.some(m => m.moduleType === 'RH');
        
        if (hasRH) {
            // Remove RH module
            console.log('\n🗑️ Step 2: Removing RH module...');
            const removeResponse = await fetch('http://localhost:5000/subscription/module/remove', {
                method: 'POST',
                headers: {
                    ...headers,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    moduleType: 'RH',
                    companyId: '9c4195cf-fe76-4455-b515-44b07224706e'
                })
            });
            
            const removeResult = await removeResponse.json();
            console.log('Remove result:', removeResult);
            
            // Wait for cache invalidation (simulating the delay we added)
            console.log('⏳ Waiting for cache invalidation...');
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } else {
            // Add RH module
            console.log('\n🔄 Step 2: Adding RH module...');
            const addResponse = await fetch('http://localhost:5000/subscription/module/add', {
                method: 'POST',
                headers: {
                    ...headers,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    moduleType: 'RH',
                    companyId: '9c4195cf-fe76-4455-b515-44b07224706e'
                })
            });
            
            const addResult = await addResponse.json();
            console.log('Add result:', addResult);
            
            // Wait for cache invalidation (simulating the delay we added)
            console.log('⏳ Waiting for cache invalidation...');
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // Step 3: Get modules again with force refresh (simulating what the UI does)
        console.log('\n📋 Step 3: Getting modules after operation (with force refresh)...');
        const timestamp = Date.now();
        const cacheBust = Math.random().toString(36).substring(7);
        
        const response2 = await fetch(`http://localhost:5000/adminDashboard/plans?companyId=9c4195cf-fe76-4455-b515-44b07224706e&_t=${timestamp}&_cache_bust=${cacheBust}`, {
            headers: {
                ...headers,
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        const data2 = await response2.json();
        console.log('Modules after operation:', data2.modules.map(m => m.moduleType));
        
        // Check if the change was reflected
        const hasRHAfter = data2.modules.some(m => m.moduleType === 'RH');
        
        if (hasRH && !hasRHAfter) {
            console.log('✅ RH module was successfully removed and UI would update correctly!');
        } else if (!hasRH && hasRHAfter) {
            console.log('✅ RH module was successfully added and UI would update correctly!');
        } else {
            console.log('❌ Module state did not change as expected');
        }
        
        console.log('\n🎯 This simulates what happens when the frontend calls loadPlanData(true)');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testUIUpdate();
