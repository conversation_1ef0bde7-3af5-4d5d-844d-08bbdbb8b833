// Script para testar remoção de módulo e verificar inconsistências
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testModuleRemoval() {
  try {
    const companyId = '9c4195cf-fe76-4455-b515-44b07224706e';
    const moduleType = 'FINANCIAL';
    
    console.log('=== ANTES DA REMOÇÃO ===');
    
    // Consulta como o AdminDashboardController faz
    const adminDashboardQuery = await prisma.subscription.findFirst({
      where: {
        companyId: companyId,
      },
      include: {
        modules: {
          where: {
            active: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            users: {
              where: {
                active: true,
              },
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    console.log('AdminDashboard - Módulos ativos:');
    adminDashboardQuery.modules.forEach(m => {
      console.log(`  - ${m.moduleType}: ${m.active ? 'ATIVO' : 'INATIVO'}`);
    });

    // Consulta como o StripeService faz
    const stripeServiceQuery = await prisma.company.findUnique({
      where: { id: companyId },
      include: {
        subscription: {
          include: {
            modules: true, // Todos os módulos, não apenas ativos
          },
        },
      },
    });

    console.log('\nStripeService - Todos os módulos:');
    stripeServiceQuery.subscription.modules.forEach(m => {
      console.log(`  - ${m.moduleType}: ${m.active ? 'ATIVO' : 'INATIVO'}`);
    });

    // Encontrar o módulo específico
    const targetModule = stripeServiceQuery.subscription.modules.find(
      m => m.moduleType === moduleType && m.active
    );

    if (!targetModule) {
      console.log(`\n❌ Módulo ${moduleType} não encontrado ou não está ativo`);
      return;
    }

    console.log(`\n=== REMOVENDO MÓDULO ${moduleType} ===`);
    console.log(`ID do módulo: ${targetModule.id}`);

    // Simular a remoção (desativar o módulo)
    await prisma.subscriptionModule.update({
      where: { id: targetModule.id },
      data: {
        active: false,
      },
    });

    console.log('✅ Módulo desativado no banco de dados');

    // Atualizar o preço da assinatura
    const currentPrice = parseFloat(stripeServiceQuery.subscription.pricePerMonth);
    const modulePrice = parseFloat(targetModule.pricePerMonth);
    const newPrice = Math.max(0, currentPrice - modulePrice);

    await prisma.subscription.update({
      where: { id: stripeServiceQuery.subscription.id },
      data: {
        pricePerMonth: newPrice,
      },
    });

    console.log(`💰 Preço atualizado: R$ ${currentPrice} -> R$ ${newPrice}`);

    console.log('\n=== APÓS A REMOÇÃO ===');

    // Verificar novamente com a consulta do AdminDashboard
    const afterRemovalAdmin = await prisma.subscription.findFirst({
      where: {
        companyId: companyId,
      },
      include: {
        modules: {
          where: {
            active: true,
          },
        },
        company: {
          select: {
            id: true,
            name: true,
            users: {
              where: {
                active: true,
              },
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    console.log('AdminDashboard - Módulos ativos após remoção:');
    afterRemovalAdmin.modules.forEach(m => {
      console.log(`  - ${m.moduleType}: ${m.active ? 'ATIVO' : 'INATIVO'}`);
    });

    // Verificar com consulta completa
    const afterRemovalComplete = await prisma.subscription.findFirst({
      where: { companyId: companyId },
      include: {
        modules: true, // Todos os módulos
      },
    });

    console.log('\nTodos os módulos após remoção:');
    afterRemovalComplete.modules.forEach(m => {
      console.log(`  - ${m.moduleType}: ${m.active ? 'ATIVO' : 'INATIVO'}`);
    });

    console.log(`\n💰 Preço final da assinatura: R$ ${afterRemovalComplete.pricePerMonth}`);

    // Verificar se o módulo FINANCIAL ainda aparece como ativo
    const financialStillActive = afterRemovalAdmin.modules.find(m => m.moduleType === 'FINANCIAL');
    if (financialStillActive) {
      console.log('\n❌ PROBLEMA: Módulo FINANCIAL ainda aparece como ativo!');
    } else {
      console.log('\n✅ Módulo FINANCIAL removido com sucesso');
    }

  } catch (error) {
    console.error('Erro no teste:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testModuleRemoval();
