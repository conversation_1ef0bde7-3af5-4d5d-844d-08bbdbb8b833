"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ban.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _selectedModule_info_monthlyPrice, _selectedModule_info, _selectedModule_info_pricePerMonth, _selectedModule_info1, _selectedModule_info2, _selectedModule_info3;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__.usePermissions)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para o modal de adicionar usuários\n    const [showAddUsersModal, setShowAddUsersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [additionalUsersCount, setAdditionalUsersCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelConfirmationText, setCancelConfirmationText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estados para o modal de confirmação de módulos\n    const [showModuleConfirmModal, setShowModuleConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [moduleAction, setModuleAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 'add' ou 'remove'\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Verificar se o usuário atual é um system_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_6__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        console.log('[DEBUG] ===== INICIANDO loadPlanData =====');\n        console.log('[DEBUG] forceRefresh:', forceRefresh);\n        console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n        console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);\n        setIsLoading(true);\n        try {\n            var _planResponse_modules, _planResponse_modules1, _planData_modules;\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);\n            console.log('[DEBUG] Fazendo chamadas para API...');\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.getPlansData(companyId, forceRefresh),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.getAvailablePlans()\n            ]);\n            console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');\n            console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));\n            console.log('[DEBUG] Módulos ativos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules = planResponse.modules) === null || _planResponse_modules === void 0 ? void 0 : _planResponse_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            console.log('[DEBUG] Quantidade de módulos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules1 = planResponse.modules) === null || _planResponse_modules1 === void 0 ? void 0 : _planResponse_modules1.length);\n            console.log('[DEBUG] ===== ATUALIZANDO ESTADO =====');\n            console.log('[DEBUG] Estado anterior planData:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map((m)=>m.moduleType));\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n            console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');\n        } catch (error) {\n            var _error_response;\n            console.error(\"[DEBUG] ===== ERRO AO CARREGAR DADOS =====\");\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            console.error(\"Error details:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n            console.log('[DEBUG] ===== FIM loadPlanData =====');\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Monitor planData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            var _planData_modules;\n            console.log('[DEBUG] ===== PLANDATA MUDOU =====');\n            console.log('[DEBUG] planData:', planData);\n            console.log('[DEBUG] Módulos no estado:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map({\n                \"PlansPage.useEffect\": (m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")\n            }[\"PlansPage.useEffect\"]));\n            console.log('[DEBUG] ================================');\n        }\n    }[\"PlansPage.useEffect\"], [\n        planData\n    ]);\n    // Função para abrir modal de adicionar usuários\n    const handleOpenAddUsersModal = ()=>{\n        setAdditionalUsersCount(1);\n        setShowAddUsersModal(true);\n    };\n    // Função para fechar modal de adicionar usuários\n    const handleCloseAddUsersModal = ()=>{\n        setShowAddUsersModal(false);\n        setAdditionalUsersCount(1);\n    };\n    // Função para abrir modal de cancelamento\n    const handleOpenCancelModal = ()=>{\n        setCancelConfirmationText('');\n        setShowCancelModal(true);\n    };\n    // Função para fechar modal de cancelamento\n    const handleCloseCancelModal = ()=>{\n        setShowCancelModal(false);\n        setCancelConfirmationText('');\n    };\n    // Função para calcular o preço adicional por usuário (baseado no preço atual)\n    const calculatePricePerUser = ()=>{\n        if (!planData) return 19.90; // Preço padrão\n        // Calcular preço por usuário baseado no plano atual\n        const currentPrice = planData.subscription.pricePerMonth;\n        const currentUsers = planData.subscription.userLimit;\n        if (currentUsers > 0) {\n            return currentPrice / currentUsers;\n        }\n        return 19.90; // Preço padrão se não conseguir calcular\n    };\n    // Função para calcular o custo adicional\n    const calculateAdditionalCost = ()=>{\n        const pricePerUser = calculatePricePerUser();\n        return pricePerUser * additionalUsersCount;\n    };\n    // Função para adicionar usuários (confirmada pelo modal)\n    const handleAddUsers = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.addUsers(additionalUsersCount, companyId);\n            const additionalCost = calculateAdditionalCost();\n            toast_success({\n                title: \"Usuários Adicionados\",\n                message: \"\".concat(additionalUsersCount, \" usu\\xe1rio(s) adicionado(s) ao plano. Custo adicional: R$ \").concat(additionalCost.toFixed(2), \"/m\\xeas.\")\n            });\n            handleCloseAddUsersModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao adicionar usuários:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Não foi possível adicionar usuários ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para abrir modal de confirmação para adicionar módulo\n    const openAddModuleConfirmation = (moduleType)=>{\n        var _availablePlans_modules;\n        const moduleInfo = availablePlans === null || availablePlans === void 0 ? void 0 : (_availablePlans_modules = availablePlans.modules) === null || _availablePlans_modules === void 0 ? void 0 : _availablePlans_modules.find((m)=>m.moduleType === moduleType);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('add');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para abrir modal de confirmação para remover módulo\n    const openRemoveModuleConfirmation = (moduleType)=>{\n        var _planData_modules;\n        const moduleInfo = planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.find((m)=>m.moduleType === moduleType);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('remove');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para fechar modal de confirmação de módulos\n    const closeModuleConfirmModal = ()=>{\n        setShowModuleConfirmModal(false);\n        setModuleAction(null);\n        setSelectedModule(null);\n    };\n    // Função para confirmar a ação do módulo\n    const confirmModuleAction = async ()=>{\n        if (!selectedModule || !moduleAction) return;\n        closeModuleConfirmModal();\n        if (moduleAction === 'add') {\n            await handleAddModule(selectedModule.type);\n        } else if (moduleAction === 'remove') {\n            await handleRemoveModule(selectedModule.type);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.addModule(moduleType, companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            await loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Removendo módulo para empresa:', companyId);\n            const result = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.removeModule(moduleType, companyId);\n            console.log('[DEBUG] Resultado da remoção:', result);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            console.log('[DEBUG] Aguardando invalidação de cache...');\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            console.log('[DEBUG] Recarregando dados do plano...');\n            await loadPlanData(true); // Force refresh para evitar cache\n            console.log('[DEBUG] Dados recarregados');\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura (confirmada pelo modal)\n    const handleCancelSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.cancelSubscription(companyId);\n            toast_success({\n                title: \"Assinatura Cancelada\",\n                message: \"Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago.\"\n            });\n            handleCloseCancelModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.reactivateSubscription(companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 416,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 415,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 428,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 440,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 442,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 433,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 432,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 453,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 425,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 469,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 482,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 484,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 475,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 474,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 494,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 466,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 515,\n                    columnNumber: 15\n                }, void 0),\n                description: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name, \".\") : \"Gerencie seu plano, usuários e módulos da assinatura.\",\n                moduleColor: \"admin\",\n                filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 531,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 533,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 524,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 523,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 513,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 549,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 553,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 552,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 547,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 560,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 566,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 575,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 581,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenCancelModal,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleReactivateSubscription,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 604,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.open('/subscription/invoices', '_blank'),\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 614,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 593,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 558,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 546,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 628,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Usu\\xe1rios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 627,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Uso atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 635,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            planData.usage.currentUsers,\n                                                            \" / \",\n                                                            planData.usage.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 634,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(planData.usage.userLimitUsage, 100), \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 639,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 638,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 644,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 633,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                children: [\n                                                    planData.usage.availableUsers,\n                                                    \" usu\\xe1rios dispon\\xedveis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 650,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenAddUsersModal,\n                                                disabled: isUpdating,\n                                                className: \"w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Adicionar Usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 653,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 649,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 626,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 688,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !isBasicModule(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>openRemoveModuleConfirmation(module.moduleType),\n                                            disabled: isUpdating,\n                                            className: \"mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Remover\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 697,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 676,\n                                    columnNumber: 13\n                                }, undefined)),\n                            availablePlans && Object.entries(availablePlans.modules).filter((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return !planData.modules.some((m)=>m.moduleType === moduleType) && !moduleInfo.included;\n                            }).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: moduleInfo.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 font-medium\",\n                                                    children: \"Dispon\\xedvel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: [\n                                                \"R$ \",\n                                                moduleInfo.monthlyPrice.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>openAddModuleConfirmation(moduleType),\n                                            disabled: isUpdating,\n                                            className: \"w-full flex items-center justify-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 740,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 735,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 716,\n                                    columnNumber: 15\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 673,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 667,\n                columnNumber: 7\n            }, undefined),\n            showModuleConfirmModal && selectedModule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: closeModuleConfirmModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 752,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-12 h-12 rounded-full mr-4 \".concat(moduleAction === 'add' ? 'bg-blue-100 dark:bg-blue-900/30' : 'bg-red-100 dark:bg-red-900/30'),\n                                                    children: moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-6 w-6 \".concat(moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-6 w-6 \".concat(moduleAction === 'add' ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                            children: moduleAction === 'add' ? 'Adicionar Módulo' : 'Remover Módulo'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: moduleAction === 'add' ? 'Esta ação afetará sua cobrança mensal' : 'Esta ação é irreversível'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 774,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModuleConfirmModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 783,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 779,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 757,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 \".concat(moduleAction === 'add' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 797,\n                                                        columnNumber: 23\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm \".concat(moduleAction === 'add' ? 'text-blue-800 dark:text-blue-200' : 'text-red-800 dark:text-red-200'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold mb-2\",\n                                                                children: moduleAction === 'add' ? '💰 ATENÇÃO: Impacto Financeiro' : '⚠️ ATENÇÃO: Consequências da Remoção'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 806,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-1 list-disc list-inside\",\n                                                                children: moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"O valor ser\\xe1 adicionado \\xe0 sua mensalidade imediatamente\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 812,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"A cobran\\xe7a ser\\xe1 proporcional ao per\\xedodo restante do ciclo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 813,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Voc\\xea ter\\xe1 acesso completo ao m\\xf3dulo ap\\xf3s a confirma\\xe7\\xe3o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 814,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"O m\\xf3dulo ficar\\xe1 ativo at\\xe9 o cancelamento manual\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 815,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Voc\\xea perder\\xe1 acesso a TODAS as funcionalidades do m\\xf3dulo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 819,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Os dados permanecer\\xe3o salvos, mas inacess\\xedveis\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 820,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Sua equipe n\\xe3o conseguir\\xe1 mais usar este m\\xf3dulo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 821,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            children: \"Para reativar, ser\\xe1 necess\\xe1rio adicionar novamente\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 822,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 795,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 833,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        moduleAction === 'add' ? 'Módulo a ser adicionado:' : 'Módulo a ser removido:'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Nome:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 838,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: getModuleName(selectedModule.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 837,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Valor mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 842,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        moduleAction === 'add' ? ((_selectedModule_info = selectedModule.info) === null || _selectedModule_info === void 0 ? void 0 : (_selectedModule_info_monthlyPrice = _selectedModule_info.monthlyPrice) === null || _selectedModule_info_monthlyPrice === void 0 ? void 0 : _selectedModule_info_monthlyPrice.toFixed(2)) || '0.00' : ((_selectedModule_info1 = selectedModule.info) === null || _selectedModule_info1 === void 0 ? void 0 : (_selectedModule_info_pricePerMonth = _selectedModule_info1.pricePerMonth) === null || _selectedModule_info_pricePerMonth === void 0 ? void 0 : _selectedModule_info_pricePerMonth.toFixed(2)) || '0.00'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 843,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 841,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        moduleAction === 'add' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Novo total mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-blue-600 dark:text-blue-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (planData.subscription.pricePerMonth + (((_selectedModule_info2 = selectedModule.info) === null || _selectedModule_info2 === void 0 ? void 0 : _selectedModule_info2.monthlyPrice) || 0)).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 851,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        moduleAction === 'remove' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Novo total mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 860,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        (planData.subscription.pricePerMonth - (((_selectedModule_info3 = selectedModule.info) === null || _selectedModule_info3 === void 0 ? void 0 : _selectedModule_info3.pricePerMonth) || 0)).toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 861,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 859,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 836,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 831,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Confirma\\xe7\\xe3o necess\\xe1ria\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: moduleAction === 'add' ? 'Tem certeza de que deseja adicionar este módulo? O valor será cobrado imediatamente.' : 'Tem certeza de que deseja remover este módulo? Esta ação não pode ser desfeita facilmente.'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 871,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 870,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 788,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: closeModuleConfirmModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 888,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: confirmModuleAction,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-white rounded-md transition-colors disabled:opacity-50 flex items-center \".concat(moduleAction === 'add' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-red-600 hover:bg-red-700'),\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Processando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    moduleAction === 'add' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 912,\n                                                        columnNumber: 25\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    moduleAction === 'add' ? 'Confirmar Adição' : 'Confirmar Remoção'\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 895,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 887,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 755,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 754,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 750,\n                columnNumber: 9\n            }, undefined),\n            showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: handleCloseCancelModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 930,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full mr-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-600 dark:text-red-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                            children: \"Cancelar Assinatura\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                            children: \"Esta a\\xe7\\xe3o \\xe9 irrevers\\xedvel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 944,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 940,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 936,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseCancelModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 953,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 949,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 935,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 962,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-red-800 dark:text-red-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-semibold mb-2\",\n                                                                children: \"⚠️ ATEN\\xc7\\xc3O: Consequ\\xeancias do Cancelamento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 964,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"space-y-1 list-disc list-inside\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Voc\\xea perder\\xe1 acesso a TODOS os m\\xf3dulos do sistema\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 966,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Todos os dados permanecer\\xe3o salvos, mas inacess\\xedveis\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Sua equipe n\\xe3o conseguir\\xe1 mais fazer login\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 968,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Relat\\xf3rios e funcionalidades ficar\\xe3o bloqueados\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 969,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"Para reativar, ser\\xe1 necess\\xe1rio contratar novamente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 970,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 963,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 961,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 960,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-3 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 979,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"O que voc\\xea est\\xe1 cancelando:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Empresa:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.company.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 985,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 983,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Valor mensal:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 988,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"R$ \",\n                                                                        planData.subscription.pricePerMonth.toFixed(2)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 989,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Usu\\xe1rios ativos:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 992,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.usage.currentUsers\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 993,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"M\\xf3dulos ativos:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 996,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.modules.length\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Pr\\xf3xima cobran\\xe7a:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1000,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 1001,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 999,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 982,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Para confirmar o cancelamento, digite \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold text-red-600 dark:text-red-400\",\n                                                            children: '\"CANCELAR ASSINATURA\"'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 59\n                                                        }, undefined),\n                                                        \" no campo abaixo:\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1013,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: cancelConfirmationText,\n                                                    onChange: (e)=>setCancelConfirmationText(e.target.value),\n                                                    placeholder: \"Digite: CANCELAR ASSINATURA\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-red-500 focus:border-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1016,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1012,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"\\xdaltima chance!\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1030,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Tem certeza de que deseja cancelar? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita e voc\\xea precisar\\xe1 contratar novamente para ter acesso ao sistema.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1031,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1027,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1026,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 958,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseCancelModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Manter Assinatura\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1039,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCancelSubscription,\n                                            disabled: isUpdating || cancelConfirmationText !== 'CANCELAR ASSINATURA',\n                                            className: \"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center\",\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1053,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Cancelando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1058,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Confirmar Cancelamento\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1046,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1038,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 933,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 932,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 928,\n                columnNumber: 9\n            }, undefined),\n            showAddUsersModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black bg-opacity-50\",\n                        onClick: handleCloseAddUsersModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1073,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 z-[11050]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1080,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar Usu\\xe1rios ao Plano\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1079,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseAddUsersModal,\n                                            className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1087,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1083,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1078,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                                    children: \"Plano Atual\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1095,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Usu\\xe1rios atuais: \",\n                                                                planData.usage.currentUsers,\n                                                                \" / \",\n                                                                planData.usage.userLimit\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1097,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Pre\\xe7o atual: R$ \",\n                                                                planData.subscription.pricePerMonth.toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1098,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Pre\\xe7o por usu\\xe1rio: R$ \",\n                                                                calculatePricePerUser().toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1094,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Quantidade de usu\\xe1rios a adicionar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1105,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setAdditionalUsersCount(Math.max(1, additionalUsersCount - 1)),\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1113,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1109,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"1\",\n                                                            max: \"100\",\n                                                            value: additionalUsersCount,\n                                                            onChange: (e)=>setAdditionalUsersCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                            className: \"w-20 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1115,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setAdditionalUsersCount(Math.min(100, additionalUsersCount + 1)),\n                                                            className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1127,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1123,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1108,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1104,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                                    children: \"Resumo da Altera\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1134,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Usu\\xe1rios adicionais: \",\n                                                                additionalUsersCount\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1136,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                \"Custo adicional: R$ \",\n                                                                calculateAdditionalCost().toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1137,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold\",\n                                                            children: [\n                                                                \"Novo total: R$ \",\n                                                                (planData.subscription.pricePerMonth + calculateAdditionalCost()).toFixed(2),\n                                                                \"/m\\xeas\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1138,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-blue-600 dark:text-blue-300 mt-2\",\n                                                            children: \"* A cobran\\xe7a ser\\xe1 proporcional ao per\\xedodo restante do ciclo atual\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1141,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1133,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1150,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Aten\\xe7\\xe3o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1152,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Esta a\\xe7\\xe3o ir\\xe1 aumentar o valor da sua assinatura mensalmente. A cobran\\xe7a adicional ser\\xe1 aplicada imediatamente de forma proporcional.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1149,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1092,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3 mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleCloseAddUsersModal,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1161,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddUsers,\n                                            disabled: isUpdating,\n                                            className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center\",\n                                            children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"animate-spin h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1175,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Processando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1180,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Confirmar Adi\\xe7\\xe3o\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1168,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1160,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1076,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1075,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1071,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 511,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"mqWLcT6Lta2Ttk/IuZshgxHV5gk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n// Função auxiliar para verificar se é módulo básico\nconst isBasicModule = (moduleType)=>{\n    return [\n        'BASIC',\n        'ADMIN',\n        'SCHEDULING'\n    ].includes(moduleType);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});