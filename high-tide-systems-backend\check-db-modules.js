// Script para verificar módulos diretamente no banco de dados
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkModules() {
    try {
        const companyId = '9c4195cf-fe76-4455-b515-44b07224706e';
        
        console.log('🔍 Verificando módulos diretamente no banco de dados...\n');
        
        // Buscar todos os módulos (ativos e inativos)
        const subscription = await prisma.subscription.findFirst({
            where: { companyId },
            include: {
                modules: true // Todos os módulos, não apenas ativos
            }
        });
        
        if (!subscription) {
            console.log('❌ Assinatura não encontrada!');
            return;
        }
        
        console.log('📋 Todos os módulos no banco de dados:');
        subscription.modules.forEach(module => {
            console.log(`  - ${module.moduleType}: ${module.active ? 'ATIVO' : 'INATIVO'} (Price: R$ ${module.pricePerMonth})`);
        });
        
        console.log(`\n💰 Preço total da assinatura: R$ ${subscription.pricePerMonth}`);
        
        // Buscar apenas módulos ativos (como o endpoint faz)
        const activeModules = subscription.modules.filter(m => m.active);
        console.log('\n📋 Módulos ativos (como retornado pelo endpoint):');
        activeModules.forEach(module => {
            console.log(`  - ${module.moduleType}: R$ ${module.pricePerMonth}`);
        });
        
    } catch (error) {
        console.error('❌ Erro:', error);
    } finally {
        await prisma.$disconnect();
    }
}

checkModules();
