# Test script to add module to subscription
$headers = @{
    'Accept' = 'application/json, text/plain, */*'
    'Accept-Language' = 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7'
    'Authorization' = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjcwNjE1Mzc0LThkMjMtNGE0NC1iMmM5LWNhN2Y2MzNlYzAxZSIsImlhdCI6MTc0OTk5Nzc2MSwiaXNDbGllbnQiOmZhbHNlLCJtb2R1bGVzIjpbIkJBU0lDIiwiQURNSU4iLCJSSCIsIkZJTkFOQ0lBTCIsIlNDSEVEVUxJTkciXSwicm9sZSI6IlNZU1RFTV9BRE1JTiIsImV4cCI6MTc1MDA4NDE2MX0.9ZnELdYO8_AJbu9BYV8yh5ne6LhE1pWzh7ty0S3yp2c'
    'Content-Type' = 'application/json'
    'Origin' = 'http://localhost:3000'
    'Referer' = 'http://localhost:3000/'
}

$body = @{
    moduleType = "RH"
    companyId = "9c4195cf-fe76-4455-b515-44b07224706e"
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri 'http://localhost:5000/subscription/module/add' -Method Post -Headers $headers -Body $body
    Write-Host "Success:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "Error:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body:" $responseBody
    }
}
