"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/introduction/page",{

/***/ "(app-pages-browser)/./src/components/settings/CompanyFormModal.js":
/*!*****************************************************!*\
  !*** ./src/components/settings/CompanyFormModal.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Globe,Loader2,Mail,MapPin,Phone,Trash,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/modules/admin/services/companyLogoService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyLogoService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Componente para input com máscara simples\nconst SimpleMaskedInput = (param)=>{\n    let { name, value, onChange, mask, placeholder, className, disabled } = param;\n    const formatWithMask = (val, mask)=>{\n        if (!val) return '';\n        let formatted = '';\n        let valIndex = 0;\n        for(let i = 0; i < mask.length && valIndex < val.length; i++){\n            if (mask[i] === '9') {\n                formatted += val[valIndex++] || '';\n            } else {\n                formatted += mask[i];\n                // Skip character in value if it matches the mask\n                if (val[valIndex] === mask[i]) valIndex++;\n            }\n        }\n        return formatted;\n    };\n    const handleChange = (e)=>{\n        // Extract only digits\n        const digits = e.target.value.replace(/\\D/g, '');\n        // Apply mask\n        const formattedValue = formatWithMask(digits, mask);\n        onChange({\n            target: {\n                name,\n                value: formattedValue\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: \"text\",\n        name: name,\n        value: value,\n        onChange: handleChange,\n        placeholder: placeholder,\n        className: className,\n        disabled: disabled\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_c = SimpleMaskedInput;\nconst CompanyFormModal = (param)=>{\n    let { isOpen, onClose, company, onSuccess } = param;\n    _s();\n    // Proteção contra renderização no servidor\n    if (false) {}\n    if (!isOpen) return null;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        tradingName: '',\n        cnpj: '',\n        phone: '',\n        phone2: '',\n        address: '',\n        city: '',\n        state: '',\n        postalCode: '',\n        website: '',\n        primaryColor: '#FF9933',\n        secondaryColor: '#3B82F6',\n        description: '',\n        email: '',\n        logo: null\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logoPreview, setLogoPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Função para processar campos JSON que podem vir como string\n    const parseJsonField = function(jsonData) {\n        let fieldName = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'campo';\n        if (!jsonData) return {};\n        // Se já for um objeto, retornar como está\n        if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {\n            return jsonData;\n        }\n        // Se for uma string, tentar fazer o parse\n        if (typeof jsonData === 'string') {\n            try {\n                return JSON.parse(jsonData);\n            } catch (error) {\n                console.error(\"[CompanyFormModal] Erro ao fazer parse do \".concat(fieldName, \":\"), error);\n                return {};\n            }\n        }\n        return {};\n    };\n    // Carregar dados da empresa para edição\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CompanyFormModal.useEffect\": ()=>{\n            if (company) {\n                // Extrair redes sociais do objeto socialMedia\n                const socialMedia = parseJsonField(company.socialMedia, 'socialMedia');\n                console.log('[CompanyFormModal] socialMedia processado:', socialMedia);\n                setFormData({\n                    name: company.name || '',\n                    tradingName: company.tradingName || '',\n                    cnpj: company.cnpj || '',\n                    phone: company.phone || '',\n                    phone2: company.phone2 || '',\n                    address: company.address || '',\n                    city: company.city || '',\n                    state: company.state || '',\n                    postalCode: company.postalCode || '',\n                    website: company.website || '',\n                    primaryColor: company.primaryColor || '#FF9933',\n                    secondaryColor: company.secondaryColor || '#3B82F6',\n                    description: company.description || '',\n                    email: company.email || '',\n                    facebook: socialMedia.facebook || '',\n                    instagram: socialMedia.instagram || '',\n                    linkedin: socialMedia.linkedin || '',\n                    twitter: socialMedia.twitter || '',\n                    youtube: socialMedia.youtube || '',\n                    logo: null\n                });\n                // Se a empresa tem um logo, definir a pré-visualização\n                if (company.documents && company.documents[0]) {\n                    const logoUrl = _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_3__.companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path);\n                    setLogoPreview(logoUrl);\n                }\n            } else {\n                // Reset do formulário para nova empresa\n                resetForm();\n            }\n        }\n    }[\"CompanyFormModal.useEffect\"], [\n        company,\n        isOpen\n    ]);\n    const resetForm = ()=>{\n        setFormData({\n            name: '',\n            tradingName: '',\n            cnpj: '',\n            phone: '',\n            phone2: '',\n            address: '',\n            city: '',\n            state: '',\n            postalCode: '',\n            website: '',\n            primaryColor: '#FF9933',\n            secondaryColor: '#3B82F6',\n            description: '',\n            email: '',\n            facebook: '',\n            instagram: '',\n            linkedin: '',\n            twitter: '',\n            youtube: '',\n            logo: null\n        });\n        setLogoPreview(null);\n        setErrors({});\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name) {\n            newErrors.name = 'O nome da empresa é obrigatório';\n        }\n        if (formData.cnpj && formData.cnpj.replace(/\\D/g, '').length !== 14) {\n            newErrors.cnpj = 'CNPJ inválido';\n        }\n        if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Email inválido';\n        }\n        if (formData.website && !/^(https?:\\/\\/)?([\\da-z.-]+)\\.([a-z.]{2,6})([/\\w .-]*)*\\/?$/.test(formData.website)) {\n            newErrors.website = 'Website inválido';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Limpar erro ao editar o campo\n        if (errors[name]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n    };\n    const handleFileChange = (e)=>{\n        const file = e.target.files[0];\n        if (!file) return;\n        // Verificar tipo de arquivo (apenas imagens)\n        if (!file.type.startsWith('image/')) {\n            setErrors((prev)=>({\n                    ...prev,\n                    logo: 'Por favor, selecione uma imagem válida'\n                }));\n            return;\n        }\n        // Limitar tamanho (2MB)\n        if (file.size > 2 * 1024 * 1024) {\n            setErrors((prev)=>({\n                    ...prev,\n                    logo: 'A imagem deve ter no máximo 2MB'\n                }));\n            return;\n        }\n        // Criar uma URL para pré-visualização\n        const objectUrl = URL.createObjectURL(file);\n        setLogoPreview(objectUrl);\n        setFormData((prev)=>({\n                ...prev,\n                logo: file\n            }));\n        // Limpar erro, se houver\n        if (errors.logo) {\n            setErrors((prev)=>({\n                    ...prev,\n                    logo: undefined\n                }));\n        }\n    };\n    const removeLogo = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                logo: null\n            }));\n        setLogoPreview(null);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsLoading(true);\n        try {\n            // Criar FormData para envio de arquivos\n            const formDataToSend = new FormData();\n            // Adicionar todos os campos do formulário\n            Object.keys(formData).forEach((key)=>{\n                if (key === 'logo') {\n                    if (formData.logo) {\n                        formDataToSend.append('logo', formData.logo);\n                    }\n                } else if (formData[key] !== null && formData[key] !== '') {\n                    formDataToSend.append(key, formData[key]);\n                }\n            });\n            // Adicionar campos JSON para redes sociais\n            const socialMedia = {\n                facebook: formData.facebook || '',\n                instagram: formData.instagram || '',\n                linkedin: formData.linkedin || '',\n                twitter: formData.twitter || '',\n                youtube: formData.youtube || ''\n            };\n            formDataToSend.append('socialMedia', JSON.stringify(socialMedia));\n            if (company) {\n                // Atualizar empresa existente\n                await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_2__.companyService.updateCompany(company.id, formDataToSend);\n            } else {\n                // Criar nova empresa\n                await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_2__.companyService.createCompany(formDataToSend);\n            }\n            onSuccess();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao salvar empresa:', error);\n            // Tratar erros da API\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) {\n                const apiErrors = {};\n                error.response.data.errors.forEach((err)=>{\n                    apiErrors[err.param] = err.msg;\n                });\n                setErrors(apiErrors);\n            } else {\n                var _error_response_data1, _error_response1;\n                setErrors({\n                    submit: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || 'Erro ao salvar empresa'\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const inputClasses = \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 text-neutral-900 dark:text-gray-100 dark:bg-gray-700\";\n    const labelClasses = \"block text-sm font-medium text-neutral-700 dark:text-gray-200 mb-1\";\n    const errorClasses = \"mt-1 text-xs text-red-600 dark:text-red-400\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-3xl w-full max-h-[90vh] flex flex-col z-[11050]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-neutral-800 dark:text-white flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    company ? 'Editar Empresa' : 'Nova Empresa'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            children: [\n                                errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg\",\n                                    children: errors.submit\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary-500 dark:text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"name\",\n                                                    children: \"Nome da Empresa *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"name\",\n                                                    name: \"name\",\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: handleChange,\n                                                    className: \"\".concat(inputClasses, \" \").concat(errors.name ? \"border-red-500 dark:border-red-700\" : \"\"),\n                                                    placeholder: \"Nome oficial da empresa\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: errorClasses,\n                                                    children: errors.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"tradingName\",\n                                                    children: \"Nome Fantasia\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"tradingName\",\n                                                    name: \"tradingName\",\n                                                    type: \"text\",\n                                                    value: formData.tradingName,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Nome comercial ou fantasia\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"cnpj\",\n                                                    children: \"CNPJ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleMaskedInput, {\n                                                    name: \"cnpj\",\n                                                    mask: \"99.999.999/9999-99\",\n                                                    value: formData.cnpj,\n                                                    onChange: handleChange,\n                                                    placeholder: \"00.000.000/0000-00\",\n                                                    className: \"\".concat(inputClasses, \" \").concat(errors.cnpj ? \"border-red-500 dark:border-red-700\" : \"\"),\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.cnpj && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: errorClasses,\n                                                    children: errors.cnpj\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"email\",\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"email\",\n                                                            name: \"email\",\n                                                            type: \"email\",\n                                                            value: formData.email,\n                                                            onChange: handleChange,\n                                                            className: \"\".concat(inputClasses, \" pl-10 \").concat(errors.email ? \"border-red-500 dark:border-red-700\" : \"\"),\n                                                            placeholder: \"<EMAIL>\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: errorClasses,\n                                                    children: errors.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 34\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary-500 dark:text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Endere\\xe7o\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"address\",\n                                                    children: \"Endere\\xe7o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"address\",\n                                                    name: \"address\",\n                                                    type: \"text\",\n                                                    value: formData.address,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Rua, n\\xfamero, complemento\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 434,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"city\",\n                                                    children: \"Cidade\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"city\",\n                                                    name: \"city\",\n                                                    type: \"text\",\n                                                    value: formData.city,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Cidade\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"state\",\n                                                    children: \"Estado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"state\",\n                                                    name: \"state\",\n                                                    type: \"text\",\n                                                    value: formData.state,\n                                                    onChange: handleChange,\n                                                    className: inputClasses,\n                                                    placeholder: \"Estado\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"postalCode\",\n                                                    children: \"CEP\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleMaskedInput, {\n                                                    name: \"postalCode\",\n                                                    mask: \"99999-999\",\n                                                    value: formData.postalCode,\n                                                    onChange: handleChange,\n                                                    placeholder: \"00000-000\",\n                                                    className: inputClasses,\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"phone\",\n                                                    children: \"Telefone\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleMaskedInput, {\n                                                            name: \"phone\",\n                                                            mask: \"(99) 99999-9999\",\n                                                            value: formData.phone,\n                                                            onChange: handleChange,\n                                                            placeholder: \"(00) 00000-0000\",\n                                                            className: \"\".concat(inputClasses, \" pl-10\"),\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"website\",\n                                                    children: \"Website\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"website\",\n                                                            name: \"website\",\n                                                            type: \"url\",\n                                                            value: formData.website,\n                                                            onChange: handleChange,\n                                                            className: \"\".concat(inputClasses, \" pl-10 \").concat(errors.website ? \"border-red-500 dark:border-red-700\" : \"\"),\n                                                            placeholder: \"https://www.empresa.com.br\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: errorClasses,\n                                                    children: errors.website\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"phone2\",\n                                                    children: \"Telefone Secund\\xe1rio\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleMaskedInput, {\n                                                            name: \"phone2\",\n                                                            mask: \"(99) 99999-9999\",\n                                                            value: formData.phone2,\n                                                            onChange: handleChange,\n                                                            placeholder: \"(00) 00000-0000\",\n                                                            className: \"\".concat(inputClasses, \" pl-10\"),\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"h-4 w-4 rounded-full bg-primary-500 dark:bg-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cores e Identidade Visual\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                lineNumber: 567,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"primaryColor\",\n                                                    children: \"Cor Prim\\xe1ria\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"primaryColor\",\n                                                            name: \"primaryColor\",\n                                                            type: \"color\",\n                                                            value: formData.primaryColor,\n                                                            onChange: handleChange,\n                                                            className: \"w-10 h-10 rounded-md\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.primaryColor,\n                                                            onChange: handleChange,\n                                                            name: \"primaryColor\",\n                                                            className: inputClasses,\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"secondaryColor\",\n                                                    children: \"Cor Secund\\xe1ria\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"secondaryColor\",\n                                                            name: \"secondaryColor\",\n                                                            type: \"color\",\n                                                            value: formData.secondaryColor,\n                                                            onChange: handleChange,\n                                                            className: \"w-10 h-10 rounded-md\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: formData.secondaryColor,\n                                                            onChange: handleChange,\n                                                            name: \"secondaryColor\",\n                                                            className: inputClasses,\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 600,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    children: \"Logo da Empresa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 flex items-center gap-4\",\n                                                    children: [\n                                                        logoPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: logoPreview,\n                                                                    alt: \"Logo preview\",\n                                                                    className: \"h-24 w-24 object-contain border border-neutral-300 dark:border-gray-600 rounded-md\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: removeLogo,\n                                                                    className: \"absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                    lineNumber: 638,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-24 w-24 border-2 border-dashed border-neutral-300 dark:border-gray-600 rounded-md flex items-center justify-center text-neutral-400 dark:text-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"h-10 w-10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"logo-upload\",\n                                                                    className: \"cursor-pointer px-4 py-2 bg-neutral-100 dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-md text-neutral-700 dark:text-gray-200 hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Fazer upload\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            id: \"logo-upload\",\n                                                                            type: \"file\",\n                                                                            accept: \"image/*\",\n                                                                            onChange: handleFileChange,\n                                                                            className: \"hidden\",\n                                                                            disabled: isLoading\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                            lineNumber: 659,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                                    children: \"JPG, PNG ou GIF. M\\xe1ximo 2MB.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.logo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: errorClasses,\n                                                    children: errors.logo\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 673,\n                                                    columnNumber: 33\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-base font-medium text-neutral-800 dark:text-gray-200 mb-4 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 text-primary-500 dark:text-primary-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Redes Sociais\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                lineNumber: 678,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"facebook\",\n                                                    children: \"Facebook\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 686,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"facebook\",\n                                                            name: \"facebook\",\n                                                            type: \"text\",\n                                                            value: formData.facebook,\n                                                            onChange: handleChange,\n                                                            className: \"\".concat(inputClasses, \" pl-10\"),\n                                                            placeholder: \"URL do Facebook\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 689,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"instagram\",\n                                                    children: \"Instagram\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 713,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"instagram\",\n                                                            name: \"instagram\",\n                                                            type: \"text\",\n                                                            value: formData.instagram,\n                                                            onChange: handleChange,\n                                                            className: \"\".concat(inputClasses, \" pl-10\"),\n                                                            placeholder: \"URL do Instagram\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 707,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"linkedin\",\n                                                    children: \"LinkedIn\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"linkedin\",\n                                                            name: \"linkedin\",\n                                                            type: \"text\",\n                                                            value: formData.linkedin,\n                                                            onChange: handleChange,\n                                                            className: \"\".concat(inputClasses, \" pl-10\"),\n                                                            placeholder: \"URL do LinkedIn\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"twitter\",\n                                                    children: \"Twitter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"twitter\",\n                                                            name: \"twitter\",\n                                                            type: \"text\",\n                                                            value: formData.twitter,\n                                                            onChange: handleChange,\n                                                            className: \"\".concat(inputClasses, \" pl-10\"),\n                                                            placeholder: \"URL do Twitter\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 751,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"youtube\",\n                                                    children: \"YouTube\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 774,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"youtube\",\n                                                            name: \"youtube\",\n                                                            type: \"text\",\n                                                            value: formData.youtube,\n                                                            onChange: handleChange,\n                                                            className: \"\".concat(inputClasses, \" pl-10\"),\n                                                            placeholder: \"URL do YouTube\",\n                                                            disabled: isLoading\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 773,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"md:col-span-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: labelClasses,\n                                                    htmlFor: \"description\",\n                                                    children: \"Descri\\xe7\\xe3o da Empresa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    name: \"description\",\n                                                    value: formData.description,\n                                                    onChange: handleChange,\n                                                    rows: \"4\",\n                                                    className: inputClasses,\n                                                    placeholder: \"Breve descri\\xe7\\xe3o sobre a empresa\",\n                                                    disabled: isLoading\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 795,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onClose,\n                                className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-200 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                disabled: isLoading,\n                                children: \"Cancelar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                lineNumber: 816,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleSubmit,\n                                className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\",\n                                disabled: isLoading,\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Globe_Loader2_Mail_MapPin_Phone_Trash_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 16,\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 832,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Salvando...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                            lineNumber: 833,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Salvar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                    lineNumber: 836,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                                lineNumber: 824,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                        lineNumber: 815,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyFormModal.js\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompanyFormModal, \"lZSRJe7VTRTOLVgi+r+bKvA3nVQ=\");\n_c1 = CompanyFormModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompanyFormModal);\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleMaskedInput\");\n$RefreshReg$(_c1, \"CompanyFormModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/CompanyFormModal.js\n"));

/***/ })

});