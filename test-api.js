// Simple test script to test the add module API
// Using built-in fetch (Node.js 18+)

async function testRemoveModule() {
    try {
        console.log('🔍 Testing module removal...');

        // First, check current modules
        const plansResponse = await fetch('http://localhost:5000/adminDashboard/plans?companyId=9c4195cf-fe76-4455-b515-44b07224706e', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjcwNjE1Mzc0LThkMjMtNGE0NC1iMmM5LWNhN2Y2MzNlYzAxZSIsImlhdCI6MTc0OTk5Nzc2MSwiaXNDbGllbnQiOmZhbHNlLCJtb2R1bGVzIjpbIkJBU0lDIiwiQURNSU4iLCJSSCIsIkZJTkFOQ0lBTCIsIlNDSEVEVUxJTkciXSwicm9sZSI6IlNZU1RFTV9BRE1JTiIsImV4cCI6MTc1MDA4NDE2MX0.9ZnELdYO8_AJbu9BYV8yh5ne6LhE1pWzh7ty0S3yp2c'
            }
        });

        const plansData = await plansResponse.json();
        console.log('📋 Current modules before removal:');
        plansData.modules.forEach(m => {
            console.log(`  - ${m.moduleType}: ${m.active ? 'ACTIVE' : 'INACTIVE'} (Price: R$ ${m.pricePerMonth})`);
        });

        // First, try to add RH module back
        console.log('🔄 Adding RH module back...');
        const addResponse = await fetch('http://localhost:5000/subscription/module/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjcwNjE1Mzc0LThkMjMtNGE0NC1iMmM5LWNhN2Y2MzNlYzAxZSIsImlhdCI6MTc0OTk5Nzc2MSwiaXNDbGllbnQiOmZhbHNlLCJtb2R1bGVzIjpbIkJBU0lDIiwiQURNSU4iLCJSSCIsIkZJTkFOQ0lBTCIsIlNDSEVEVUxJTkciXSwicm9sZSI6IlNZU1RFTV9BRE1JTiIsImV4cCI6MTc1MDA4NDE2MX0.9ZnELdYO8_AJbu9BYV8yh5ne6LhE1pWzh7ty0S3yp2c'
            },
            body: JSON.stringify({
                moduleType: 'RH',
                companyId: '9c4195cf-fe76-4455-b515-44b07224706e'
            })
        });

        const addData = await addResponse.json();
        if (addResponse.ok) {
            console.log('✅ Add Success:', addData);
        } else {
            console.log('ℹ️ Add Result:', addData);
        }

        // Wait a moment for cache to be cleared
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Try to remove RH module
        console.log('🗑️ Now removing RH module...');
        const removeResponse = await fetch('http://localhost:5000/subscription/module/remove', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjcwNjE1Mzc0LThkMjMtNGE0NC1iMmM5LWNhN2Y2MzNlYzAxZSIsImlhdCI6MTc0OTk5Nzc2MSwiaXNDbGllbnQiOmZhbHNlLCJtb2R1bGVzIjpbIkJBU0lDIiwiQURNSU4iLCJSSCIsIkZJTkFOQ0lBTCIsIlNDSEVEVUxJTkciXSwicm9sZSI6IlNZU1RFTV9BRE1JTiIsImV4cCI6MTc1MDA4NDE2MX0.9ZnELdYO8_AJbu9BYV8yh5ne6LhE1pWzh7ty0S3yp2c'
            },
            body: JSON.stringify({
                moduleType: 'RH',
                companyId: '9c4195cf-fe76-4455-b515-44b07224706e'
            })
        });

        const removeData = await removeResponse.json();

        if (removeResponse.ok) {
            console.log('✅ Remove Success:', removeData);
        } else {
            console.log('❌ Remove Error:', removeData);
        }

        // Check modules after removal
        const plansResponseAfter = await fetch('http://localhost:5000/adminDashboard/plans?companyId=9c4195cf-fe76-4455-b515-44b07224706e', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjcwNjE1Mzc0LThkMjMtNGE0NC1iMmM5LWNhN2Y2MzNlYzAxZSIsImlhdCI6MTc0OTk5Nzc2MSwiaXNDbGllbnQiOmZhbHNlLCJtb2R1bGVzIjpbIkJBU0lDIiwiQURNSU4iLCJSSCIsIkZJTkFOQ0lBTCIsIlNDSEVEVUxJTkciXSwicm9sZSI6IlNZU1RFTV9BRE1JTiIsImV4cCI6MTc1MDA4NDE2MX0.9ZnELdYO8_AJbu9BYV8yh5ne6LhE1pWzh7ty0S3yp2c'
            }
        });

        const plansDataAfter = await plansResponseAfter.json();
        console.log('📋 Current modules after removal:');
        plansDataAfter.modules.forEach(m => {
            console.log(`  - ${m.moduleType}: ${m.active ? 'ACTIVE' : 'INACTIVE'} (Price: R$ ${m.pricePerMonth})`);
        });

        console.log(`💰 Subscription price: R$ ${plansData.subscription.pricePerMonth} -> R$ ${plansDataAfter.subscription.pricePerMonth}`);

    } catch (error) {
        console.error('❌ Network Error:', error.message);
    }
}

testRemoveModule();
