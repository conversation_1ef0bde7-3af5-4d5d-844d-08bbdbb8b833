// Script para corrigir assinaturas de teste adicionando IDs fictícios do Stripe
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixTestSubscriptions() {
  try {
    console.log('Buscando assinaturas sem stripeSubscriptionId...');
    
    // Buscar assinaturas que não têm stripeSubscriptionId
    const subscriptionsWithoutStripe = await prisma.subscription.findMany({
      where: {
        OR: [
          { stripeSubscriptionId: null },
          { stripeCustomerId: null }
        ]
      },
      include: {
        company: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    console.log(`Encontradas ${subscriptionsWithoutStripe.length} assinaturas para corrigir`);

    for (const subscription of subscriptionsWithoutStripe) {
      // Gerar IDs fictícios do Stripe para teste
      const fakeStripeCustomerId = `cus_test_${subscription.companyId.substring(0, 8)}`;
      const fakeStripeSubscriptionId = `sub_test_${subscription.id.substring(0, 8)}`;
      
      console.log(`Atualizando assinatura da empresa: ${subscription.company.name}`);
      
      // Atualizar a assinatura com IDs fictícios
      await prisma.subscription.update({
        where: {
          id: subscription.id
        },
        data: {
          stripeCustomerId: fakeStripeCustomerId,
          stripeSubscriptionId: fakeStripeSubscriptionId,
          stripeCurrentPeriodEnd: subscription.nextBillingDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        }
      });

      console.log(`  - Customer ID: ${fakeStripeCustomerId}`);
      console.log(`  - Subscription ID: ${fakeStripeSubscriptionId}`);
    }

    console.log('\n=== RESUMO DAS CORREÇÕES ===');
    
    // Listar todas as assinaturas atualizadas
    const allSubscriptions = await prisma.subscription.findMany({
      include: {
        company: {
          select: {
            name: true
          }
        },
        modules: {
          where: {
            active: true
          }
        }
      }
    });

    allSubscriptions.forEach(sub => {
      console.log(`\n${sub.company.name}:`);
      console.log(`  - Status: ${sub.status}`);
      console.log(`  - Stripe Customer: ${sub.stripeCustomerId}`);
      console.log(`  - Stripe Subscription: ${sub.stripeSubscriptionId}`);
      console.log(`  - Preço: R$ ${sub.pricePerMonth}`);
      console.log(`  - Usuários: ${sub.userLimit}`);
      console.log(`  - Módulos: ${sub.modules.map(m => m.moduleType).join(', ')}`);
    });

    console.log('\nAssinaturas de teste corrigidas com sucesso!');

  } catch (error) {
    console.error('Erro ao corrigir assinaturas:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixTestSubscriptions();
