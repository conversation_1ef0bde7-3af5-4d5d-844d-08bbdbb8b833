"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Toast */ \"(app-pages-browser)/./src/components/ui/Toast.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__.usePermissions)();\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Verificar se o usuário atual é um system_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_error)({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async ()=>{\n        setIsLoading(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__.plansService.getPlansData(companyId),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__.plansService.getAvailablePlans()\n            ]);\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n        } catch (error) {\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_error)({\n                title: \"Erro\",\n                message: \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Função para adicionar usuários\n    const handleAddUsers = async (additionalUsers)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__.plansService.addUsers(additionalUsers, companyId);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_success)({\n                title: \"Sucesso\",\n                message: \"\".concat(additionalUsers, \" usu\\xe1rio(s) adicionado(s) ao plano.\")\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao adicionar usuários:\", error);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_error)({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar usuários ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__.plansService.addModule(moduleType, companyId);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_success)({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_error)({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__.plansService.removeModule(moduleType, companyId);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_success)({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_error)({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura\n    const handleCancelSubscription = async ()=>{\n        if (!confirm(\"Tem certeza que deseja cancelar a assinatura?\")) return;\n        setIsUpdating(true);\n        try {\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__.plansService.cancelSubscription();\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_success)({\n                title: \"Sucesso\",\n                message: \"Assinatura cancelada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_error)({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_4__.plansService.reactivateSubscription();\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_success)({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            (0,_components_ui_Toast__WEBPACK_IMPORTED_MODULE_7__.toast_error)({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 279,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 291,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 293,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 284,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 283,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 320,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 333,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 335,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 326,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 325,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 350,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 317,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 366,\n                    columnNumber: 15\n                }, void 0),\n                description: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name, \".\") : \"Gerencie seu plano, usuários e módulos da assinatura.\",\n                moduleColor: \"admin\",\n                filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 382,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 384,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 375,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 374,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 400,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 426,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 425,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleCancelSubscription,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleReactivateSubscription,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 455,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.open('/subscription/invoices', '_blank'),\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 465,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Usu\\xe1rios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Uso atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            planData.usage.currentUsers,\n                                                            \" / \",\n                                                            planData.usage.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(planData.usage.userLimitUsage, 100), \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                children: [\n                                                    planData.usage.availableUsers,\n                                                    \" usu\\xe1rios dispon\\xedveis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleAddUsers(5),\n                                                disabled: isUpdating,\n                                                className: \"w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Adicionar 5 usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 542,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !isBasicModule(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleRemoveModule(module.moduleType),\n                                            disabled: isUpdating,\n                                            className: \"mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Remover\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, undefined)),\n                            availablePlans && Object.entries(availablePlans.modules).filter((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return !planData.modules.some((m)=>m.moduleType === moduleType) && !moduleInfo.included;\n                            }).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: moduleInfo.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 font-medium\",\n                                                    children: \"Dispon\\xedvel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 568,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 579,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: [\n                                                \"R$ \",\n                                                moduleInfo.monthlyPrice.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleAddModule(moduleType),\n                                            disabled: isUpdating,\n                                            className: \"w-full flex items-center justify-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 567,\n                                    columnNumber: 15\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 524,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 518,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"/mdOitQ8eQ9QoN9FvCzIn8P4Upo=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__.usePermissions\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n// Função auxiliar para verificar se é módulo básico\nconst isBasicModule = (moduleType)=>{\n    return [\n        'BASIC',\n        'ADMIN',\n        'SCHEDULING'\n    ].includes(moduleType);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});