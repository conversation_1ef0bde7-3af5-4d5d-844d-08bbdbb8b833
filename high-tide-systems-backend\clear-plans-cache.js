// Script para limpar cache relacionado aos planos
const Redis = require('redis');

async function clearPlansCache() {
  let redisClient;
  
  try {
    // Conectar ao Redis
    redisClient = Redis.createClient({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
    });

    await redisClient.connect();
    console.log('Conectado ao Redis');

    // Buscar todas as chaves relacionadas a dashboard e plans
    const dashboardKeys = await redisClient.keys('dashboard:*');
    const planKeys = await redisClient.keys('*plans*');
    const allKeys = [...dashboardKeys, ...planKeys];

    console.log(`Encontradas ${allKeys.length} chaves de cache relacionadas:`);
    allKeys.forEach(key => console.log(`  - ${key}`));

    if (allKeys.length > 0) {
      // Deletar todas as chaves encontradas
      await redisClient.del(allKeys);
      console.log(`\n✅ ${allKeys.length} chaves de cache removidas com sucesso!`);
    } else {
      console.log('\n📝 Nenhuma chave de cache encontrada');
    }

    // Verificar se as chaves foram realmente removidas
    const remainingKeys = await redisClient.keys('dashboard:*');
    console.log(`\nChaves restantes: ${remainingKeys.length}`);

  } catch (error) {
    console.error('Erro ao limpar cache:', error);
    
    // Se o Redis não estiver disponível, não é um erro crítico
    if (error.code === 'ECONNREFUSED') {
      console.log('Redis não está rodando - cache não será limpo, mas isso não afeta a funcionalidade');
    }
  } finally {
    if (redisClient) {
      try {
        await redisClient.quit();
        console.log('Conexão com Redis fechada');
      } catch (error) {
        console.log('Erro ao fechar conexão com Redis:', error.message);
      }
    }
  }
}

clearPlansCache();
