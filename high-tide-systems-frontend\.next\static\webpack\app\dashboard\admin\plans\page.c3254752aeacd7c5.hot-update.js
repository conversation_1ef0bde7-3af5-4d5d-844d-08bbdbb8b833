"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Ban,Building,Calendar,CheckCircle,CreditCard,Crown,DollarSign,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__.usePermissions)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para o modal de adicionar usuários\n    const [showAddUsersModal, setShowAddUsersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [additionalUsersCount, setAdditionalUsersCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelConfirmationText, setCancelConfirmationText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Verificar se o usuário atual é um system_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_6__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        console.log('[DEBUG] ===== INICIANDO loadPlanData =====');\n        console.log('[DEBUG] forceRefresh:', forceRefresh);\n        console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n        console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);\n        setIsLoading(true);\n        try {\n            var _planResponse_modules, _planResponse_modules1, _planData_modules;\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);\n            console.log('[DEBUG] Fazendo chamadas para API...');\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.getPlansData(companyId, forceRefresh),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.getAvailablePlans()\n            ]);\n            console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');\n            console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));\n            console.log('[DEBUG] Módulos ativos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules = planResponse.modules) === null || _planResponse_modules === void 0 ? void 0 : _planResponse_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            console.log('[DEBUG] Quantidade de módulos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules1 = planResponse.modules) === null || _planResponse_modules1 === void 0 ? void 0 : _planResponse_modules1.length);\n            console.log('[DEBUG] ===== ATUALIZANDO ESTADO =====');\n            console.log('[DEBUG] Estado anterior planData:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map((m)=>m.moduleType));\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n            console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');\n        } catch (error) {\n            var _error_response;\n            console.error(\"[DEBUG] ===== ERRO AO CARREGAR DADOS =====\");\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            console.error(\"Error details:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n            console.log('[DEBUG] ===== FIM loadPlanData =====');\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Monitor planData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            var _planData_modules;\n            console.log('[DEBUG] ===== PLANDATA MUDOU =====');\n            console.log('[DEBUG] planData:', planData);\n            console.log('[DEBUG] Módulos no estado:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map({\n                \"PlansPage.useEffect\": (m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")\n            }[\"PlansPage.useEffect\"]));\n            console.log('[DEBUG] ================================');\n        }\n    }[\"PlansPage.useEffect\"], [\n        planData\n    ]);\n    // Função para abrir modal de adicionar usuários\n    const handleOpenAddUsersModal = ()=>{\n        setAdditionalUsersCount(1);\n        setShowAddUsersModal(true);\n    };\n    // Função para fechar modal de adicionar usuários\n    const handleCloseAddUsersModal = ()=>{\n        setShowAddUsersModal(false);\n        setAdditionalUsersCount(1);\n    };\n    // Função para abrir modal de cancelamento\n    const handleOpenCancelModal = ()=>{\n        setCancelConfirmationText('');\n        setShowCancelModal(true);\n    };\n    // Função para fechar modal de cancelamento\n    const handleCloseCancelModal = ()=>{\n        setShowCancelModal(false);\n        setCancelConfirmationText('');\n    };\n    // Função para calcular o preço adicional por usuário (baseado no preço atual)\n    const calculatePricePerUser = ()=>{\n        if (!planData) return 19.90; // Preço padrão\n        // Calcular preço por usuário baseado no plano atual\n        const currentPrice = planData.subscription.pricePerMonth;\n        const currentUsers = planData.subscription.userLimit;\n        if (currentUsers > 0) {\n            return currentPrice / currentUsers;\n        }\n        return 19.90; // Preço padrão se não conseguir calcular\n    };\n    // Função para calcular o custo adicional\n    const calculateAdditionalCost = ()=>{\n        const pricePerUser = calculatePricePerUser();\n        return pricePerUser * additionalUsersCount;\n    };\n    // Função para adicionar usuários (confirmada pelo modal)\n    const handleAddUsers = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.addUsers(additionalUsersCount, companyId);\n            const additionalCost = calculateAdditionalCost();\n            toast_success({\n                title: \"Usuários Adicionados\",\n                message: \"\".concat(additionalUsersCount, \" usu\\xe1rio(s) adicionado(s) ao plano. Custo adicional: R$ \").concat(additionalCost.toFixed(2), \"/m\\xeas.\")\n            });\n            handleCloseAddUsersModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao adicionar usuários:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Não foi possível adicionar usuários ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.addModule(moduleType, companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            await loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Removendo módulo para empresa:', companyId);\n            const result = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.removeModule(moduleType, companyId);\n            console.log('[DEBUG] Resultado da remoção:', result);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            console.log('[DEBUG] Aguardando invalidação de cache...');\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            console.log('[DEBUG] Recarregando dados do plano...');\n            await loadPlanData(true); // Force refresh para evitar cache\n            console.log('[DEBUG] Dados recarregados');\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura (confirmada pelo modal)\n    const handleCancelSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.cancelSubscription(companyId);\n            toast_success({\n                title: \"Assinatura Cancelada\",\n                message: \"Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago.\"\n            });\n            handleCloseCancelModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_5__.plansService.reactivateSubscription(companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 375,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 374,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 387,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 401,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 392,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 391,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 412,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 428,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 441,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 443,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 434,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 433,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 426,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 455,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 453,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 425,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 474,\n                    columnNumber: 15\n                }, void 0),\n                description: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name, \".\") : \"Gerencie seu plano, usuários e módulos da assinatura.\",\n                moduleColor: \"admin\",\n                filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 490,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 492,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 483,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 482,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 512,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 519,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 525,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 518,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 540,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenCancelModal,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleReactivateSubscription,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.open('/subscription/invoices', '_blank'),\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 573,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 552,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 517,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Usu\\xe1rios\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 586,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Uso atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            planData.usage.currentUsers,\n                                                            \" / \",\n                                                            planData.usage.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-blue-500 h-2 rounded-full transition-all duration-300\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(planData.usage.userLimitUsage, 100), \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 598,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 597,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 603,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 592,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                                children: [\n                                                    planData.usage.availableUsers,\n                                                    \" usu\\xe1rios dispon\\xedveis\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenAddUsersModal,\n                                                disabled: isUpdating,\n                                                className: \"w-full flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Adicionar Usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 612,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 591,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 585,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 628,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 627,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 638,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 637,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 643,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 636,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 647,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 650,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !isBasicModule(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleRemoveModule(module.moduleType),\n                                            disabled: isUpdating,\n                                            className: \"mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 661,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Remover\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 635,\n                                    columnNumber: 13\n                                }, undefined)),\n                            availablePlans && Object.entries(availablePlans.modules).filter((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return !planData.modules.some((m)=>m.moduleType === moduleType) && !moduleInfo.included;\n                            }).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-400 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 678,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: moduleInfo.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 679,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 font-medium\",\n                                                    children: \"Dispon\\xedvel\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 676,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 687,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: [\n                                                \"R$ \",\n                                                moduleInfo.monthlyPrice.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 690,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleAddModule(moduleType),\n                                            disabled: isUpdating,\n                                            className: \"w-full flex items-center justify-center px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded transition-colors disabled:opacity-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-1 h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 699,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Adicionar\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 694,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 675,\n                                    columnNumber: 15\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 626,\n                columnNumber: 7\n            }, undefined),\n            showAddUsersModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 715,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Adicionar Usu\\xe1rios ao Plano\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 714,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCloseAddUsersModal,\n                                        className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 722,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 718,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 713,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-gray-100 mb-2\",\n                                                children: \"Plano Atual\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 730,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Usu\\xe1rios atuais: \",\n                                                            planData.usage.currentUsers,\n                                                            \" / \",\n                                                            planData.usage.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Pre\\xe7o atual: R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2),\n                                                            \"/m\\xeas\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Pre\\xe7o por usu\\xe1rio: R$ \",\n                                                            calculatePricePerUser().toFixed(2),\n                                                            \"/m\\xeas\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 731,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 729,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                children: \"Quantidade de usu\\xe1rios a adicionar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 740,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setAdditionalUsersCount(Math.max(1, additionalUsersCount - 1)),\n                                                        className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        min: \"1\",\n                                                        max: \"100\",\n                                                        value: additionalUsersCount,\n                                                        onChange: (e)=>setAdditionalUsersCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                        className: \"w-20 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setAdditionalUsersCount(Math.min(100, additionalUsersCount + 1)),\n                                                        className: \"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 743,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 739,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-blue-900 dark:text-blue-100 mb-2\",\n                                                children: \"Resumo da Altera\\xe7\\xe3o\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-blue-800 dark:text-blue-200 space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Usu\\xe1rios adicionais: \",\n                                                            additionalUsersCount\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"Custo adicional: R$ \",\n                                                            calculateAdditionalCost().toFixed(2),\n                                                            \"/m\\xeas\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 772,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-semibold\",\n                                                        children: [\n                                                            \"Novo total: R$ \",\n                                                            (planData.subscription.pricePerMonth + calculateAdditionalCost()).toFixed(2),\n                                                            \"/m\\xeas\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-blue-600 dark:text-blue-300 mt-2\",\n                                                        children: \"* A cobran\\xe7a ser\\xe1 proporcional ao per\\xedodo restante do ciclo atual\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 770,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 768,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-2 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"Aten\\xe7\\xe3o:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Esta a\\xe7\\xe3o ir\\xe1 aumentar o valor da sua assinatura mensalmente. A cobran\\xe7a adicional ser\\xe1 aplicada imediatamente de forma proporcional.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 788,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 786,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 784,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 783,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 727,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCloseAddUsersModal,\n                                        disabled: isUpdating,\n                                        className: \"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors disabled:opacity-50\",\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 796,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddUsers,\n                                        disabled: isUpdating,\n                                        className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors disabled:opacity-50 flex items-center\",\n                                        children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"animate-spin h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 810,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"Processando...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Ban_Building_Calendar_CheckCircle_CreditCard_Crown_DollarSign_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"Confirmar Adi\\xe7\\xe3o\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 803,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 795,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 711,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 710,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 709,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 470,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"w28icSFehlnr1VERFp0pIlxhNoY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_3__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n// Função auxiliar para verificar se é módulo básico\nconst isBasicModule = (moduleType)=>{\n    return [\n        'BASIC',\n        'ADMIN',\n        'SCHEDULING'\n    ].includes(moduleType);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});